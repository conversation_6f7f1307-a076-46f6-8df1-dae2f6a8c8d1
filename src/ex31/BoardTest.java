package ex31;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.CoreMatchers.is;

public class BoardTest {

    @Test
    public void printBoard() {
        System.out.println(getBoard());
    }

    @Test
    @Points(3)
    public void horizontalStartingFromLeftmost() {

        Board board = getBoard();

        assertThat(board.getChunkSize(2, 2), is(3));
    }

    @Test
    @Points(3)
    public void horizontalStartingFromRightmost() {

        Board board = getBoard();

        assertThat(board.getChunkSize(2, 4), is(3));
    }

    @Test
    @Points(4)
    public void verticaStartingFromTopmost() {

        Board board = getBoard();

        assertThat(board.getChunkSize(1, 6), is(4));
    }

    @Test
    @Points(2)
    public void unfilledBesidesChunk() {
        Board board = getBoard();

        assertThat(board.getChunkSize(2, 5), is(0));
    }

    @Test
    @Points(3)
    public void verticaStartingFromCenter() {

        Board board = getBoard();

        assertThat(board.getChunkSize(3, 6), is(4));
    }

    @Test
    @Points(10)
    public void canHandleEdgeSquares() {

        Board board = getBoard();

        assertThat(board.getChunkSize(5, 9), is(5));
    }

    private static Board getBoard() {
        Board board = new Board(6, 10);

        board.mark(2, 2);
        board.mark(2, 3);
        board.mark(2, 4);

        board.mark(1, 6);
        board.mark(2, 6);
        board.mark(3, 6);
        board.mark(4, 6);

        board.mark(1, 9);
        board.mark(2, 9);
        board.mark(3, 9);
        board.mark(4, 9);
        board.mark(5, 9);

        return board;
    }

}