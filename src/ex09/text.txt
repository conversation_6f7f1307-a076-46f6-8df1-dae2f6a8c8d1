Ülesanne 1 (17 punkti)

  !!!! NB! tekkis segadus sellega, mis on unikaalne nimi.
           Stiilis. tagastati list milles polnud duplilaate.

  Paketis ex1 on klass UniqueNameFinder ja test selle testimiseks.

  UniqueNameFinder-isse saab lisada Person tüüpi objekte ja sellelt saab küsida,
  kas sinna on lisatud isik sama nimega (eesnimi ja perekonnanimi kattuvad).
  <PERSON><PERSON> kohta on test findsOutWhetherThereIsAPersonWithTheSameName().

  <PERSON><PERSON> saab küsida, kui palju on lisatud unikaalse nimega isikuid.
  <PERSON><PERSON> kohta on test findsCountOfPersonsWithUniqueNames().

  <PERSON><PERSON> saab küsida, unikaalse nimega isikud.
  <PERSON><PERSON> kohta on test findsPersonsWithUniqueNames().

  <PERSON><PERSON><PERSON><PERSON> vajalik kood, et testid tööle hakkaksid.
  Muuta võite klasse UniqueNameFinder.java ja Person.java.
