ex1:
 0p - 9
 3p - 1
 4p - 1
 5p - 4
 8p - 1
15p - 1
16p - 1
20p - 8
23p - 1
25p - 14

väljade arv piirata: private int seconds;
                         private int minutes;
                         private int hours;

paljud operatsioonid on lokaalsed ja välju ei nõua.

floor(Unit unit)
  String[] times = time.split(":"); if (unit == Unit.MINUTES) {    times[2] = "00"; }

Ülesanne 1 (25 punkti)

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Paketis ex1 on klass MyDuration ja testid selle käitumise kontrollimiseks.
  MyDuration kujutab ajalist kestust nt "04:01:05" on 4 tundi 1 minut ja 5 sekundit.

  MyDuration(String value) loob uue isendi sõne põhjal

  get(unit) tagastab määratud komponendi väärtuse (tunnid, minutid, sekundid).

  add(number, unit) lisab määratud komponendile määratud väärtuse.

  floor(unit) nullib madalama taseme komponentide väärtused.

  MyDuration(int seconds) loob uue isendi sekundite põhjal. Minutis on 60 sekundit jne.

  add(MyDuration other) lisab olemasolevale kestusele teise kestuse.

  Vihje: nulli saate numbri algusse alloleva konstruktsiooniga
        String.format("%02d:%02d", 1, 23); // 01:23

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi MyDuration ja uusi faile lisada ei või.
    - Klasse paketist java.time või java.util kasutada ei või.
    - Kood peab läbima testid klassist MyDurationTest.