package ex25;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class ResultsTest {

    @Test
    @Points(6)
    public void canCreateResultsOfDifferentTypes() {

        // Results of floats

        Results<Float> results = new Results<>(3f, 0f, 6f);

        Float r1 = results.r1();
        Float r2 = results.r2();

        assertThat(r1, is(3f));
        assertThat(r2, is(0f));

        // Results of integers

        assertThat(new Results<>(2, 9, 0).r2(), is(9));
        assertThat(new Results<>(1, 0, 3).r3(), is(3));
    }
}
