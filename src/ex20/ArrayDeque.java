package ex20;

public class ArrayDeque {

    private int[] elements;
    private int head = 0;
    private int tail = 0;

    public ArrayDeque(int capacity) {
        elements = new int[capacity];
    }

    public void addLast(int element) {
        elements[tail++] = element;
    }

    public int removeLast() {
        return elements[--tail];
    }

    public int removeFirst() {
        if (size() == 0) {
            throw new IllegalStateException("no more elements");
        }

        return elements[head++];
    }

    private int size() {
        return tail - head;
    }

    public void addFirst(int element) {
        if (head == 0) {
            shiftRight();
        }

        elements[--head] = element;
    }

    private void shiftRight() {
        int[] newElements = new int[elements.length * 2];

        for (int i = 0; i < elements.length; i++) {
            newElements[i + 1] = elements[i];
        }

        elements = newElements;
        tail++;
        head++;
    }

    public int[] getContents() {
        int[] contents = new int[tail - head];

        for (int i = head; i < tail; i++) {
            contents[i - head] = elements[i];
        }

        return contents;
    }

    public int[] getSortedContents() {
        int[] contents = getContents();

        for (int i = 0; i < contents.length; i++) {
            for (int j = i + 1; j < contents.length; j++) {
                if (contents[i] > contents[j]) {
                    int temp = contents[i];
                    contents[i] = contents[j];
                    contents[j] = temp;
                }
            }
        }

        return contents;
    }
}