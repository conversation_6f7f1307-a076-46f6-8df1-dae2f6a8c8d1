package ex37;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.lang.reflect.Field;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class VersionedDocumentTest {

    @Test
    @Points(1)
    public void canAppendText() {
        VersionedDocument document = new VersionedDocument();

        document.append("Hello");
        assertThat(document.getContent()).isEqualTo("Hello");

        document.append(" World");
        assertThat(document.getContent()).isEqualTo("Hello World");
    }

    @Test
    @Points(2)
    public void canReplaceText() {
        VersionedDocument document = new VersionedDocument();

        document.append("Hello World");
        document.replace("World", "Java");

        assertThat(document.getContent()).isEqualTo("Hello Java");
    }

    @Test
    @Points(6)
    public void canDeleteText() {
        VersionedDocument document = new VersionedDocument();

        document.append("Hello World");
        document.delete(5, 11);

        assertThat(document.getContent()).isEqualTo("Hello");
    }

    @Test
    @Points(7)
    public void canGetEarlyVersions() {
        VersionedDocument document = new VersionedDocument();

        document.append("This is a sample text");
        document.replace("text", "sentence");
        document.delete(0, 8);

        assertThat(document.getVersion(0))
                .isEqualTo("This is a sample text");

        assertThat(document.getVersion(1))
                .isEqualTo("This is a sample sentence");

        assertThat(document.getVersion(2))
                .isEqualTo("a sample sentence");
    }

    @Test
    @Points(9)
    public void canGetSnapshot() {
        VersionedDocument document = new VersionedDocument();

        document.append("This is a sample text.");
        document.replace("text", "sentence");
        document.createSnapshot();
        document.delete(10, 17);
        document.append(" A second");
        document.createSnapshot();
        document.append(" sentence");
        document.append(" follows");
        document.createSnapshot();
        document.append(".");

        assertThat(document.getSnapshot(0))
                .isEqualTo("This is a sample sentence.");

        assertThat(document.getSnapshot(1))
                .isEqualTo("This is a sentence. A second");

        assertThat(document.getSnapshot(2))
                .isEqualTo("This is a sentence. A second sentence follows");
    }

    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() {
        Field[] fields = VersionedDocument.class.getDeclaredFields();

        assertThat(fields.length).isEqualTo(3);

        assertThat(fields[0].getType()).isEqualTo(String.class);
        assertThat(fields[1].getType()).isEqualTo(Modification.class);
        assertThat(fields[2].getGenericType().getTypeName())
                .isEqualTo("java.util.LinkedList<ex37.Modification>");
    }
}
