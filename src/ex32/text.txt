Ülesanne 1 (25 punkti)

  NB! Teksti lõpus on tingimused, mille eiramisel ei lähe tulemus arvesse.

  Kataloogis ex1 on klass ArrayStoreCounter. See on lihtne loendur, millele
  saab anda algväärtuse ja mille meetod increment() suurendab väärtust ühe võrra.

  Keerukust lisab tingimus, et andmete hoidmiseks on teil kasutada ainult kolmeelemendiline
  baitide massiiv (byte[]). Byte on Java andmetüüp, mille väärtused on -128 kuni 127.
  Loendur peab toetama arve nullist kuni kahe miljonini.

  Kui Te ei oska kohe täielikku lahendust välja mõtelda, siis tehke vaid
  niipalju, et esimene test läbi läheks. Esimene test peab toetama vaid lahendust,
  mis saab hakkama numbritega 0 - 100 ja see on üsna triviaalne. <PERSON><PERSON>,
  et teine test läbi läheks jne. Testid on toodud ülesande keerukuse järjekorras.

  Täisarvu teisendamiseks baidiks on Integer.valueOf(integerValue).byteValue().

  NB! Tingimused, mille eiramisel ei lähe tulemus arvesse.
    - Muuta võite faili ArrayStoreCounter.java. Uusi faile luua ei tohi.
    - Klassil võib olla vaid üks väli, milleks on byte tüüpi massiiv suurusega kolm.
    - Kood peab läbima testid klassist ArrayStoreCounterTest.
