Ülesanne 2 (25 punkti)

  Klass NumberRepository hoiab väljadel simpleNumberData ja scientificNumberData
  andmeid kahte tüüpi numbrite kohta.

  V<PERSON>ljal simpleNumberData on andmed lihtnumbrite kohta.
  Väljal scientificNumberData on andmed numbrite kohta,
  mis koosnevad kahest osast: tüvenumber ja kümne aste.
  Nt. "2e3" on 2 * 10^3 on 2000.

  SimpleNumber ja ScientificNumber on klassid vastavate numbrite kujutamiseks.

  NumberRepositoryTest küsib NumberRepository käest numbreid ja liidab
  need kokku.

  <PERSON><PERSON> on kirjutada lõpuni klass NumberRepository, mis sõne
  kujul olevad numbrid vastavateks objektideks teisendab ja need objektid
  väljastab.

  Meetod getAllNumbers() peab tagastama listi objektidest, mis on klassist
  SimpleNumber või ScientificNumber. Mitte nt. klassist Integer.

  Muuta võite klasse NumberRepository.java, ScientificNumber.java ja SimpleNumber.java
  Uusi klasse luua ei tohi.
