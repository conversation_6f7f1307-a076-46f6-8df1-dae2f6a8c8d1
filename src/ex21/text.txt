  Paketis ex1 on klass WordSearch ja testid selle käitumise kontrollimiseks.
  WordSearch laeb objekti loomisel failist words.txt sõnade loetelu.
  Nende sõnade hulgast on võimalik hiljem otsida sõnu, mis vastavad otsingumustrile.

  NB! Selles ülesandes ei tohi kasutada Javaga kaasasolevaid sõnede otsimise meetodeid.
  Peaksite kasutama sümbolite ükshaaval läbikäimist ja võrdlemist.
  Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Meetod find(<muster>) tagastab kõik sõnad, mis vastavad otsingumustrile.

  Muster võib olla järgmine:

  abc - otsitakse sõna, mis sisaldab täpselt neid tähti selles järjekorras
  ab* - otsitakse sõna, mis algab tähtedega "ab", millele järgneb täpselt üks täht.
        Näiteks sobib sõna "abc" aga mitte "abcd".
  ab** - o<PERSON><PERSON><PERSON><PERSON> sõna, mis algab tähtedega "ab" ja millele järgneb kuitahes palju tähti.
         Näiteks sobib sõna "abettor", "abound", jne.
  **ing - otsitakse sõna, mis lõppeb tähtedega "ing" ja millele eelneb kuitahes palju tähti.
         Näiteks sobib sõna "accomplishing".

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi WordSearch ja uusi klasse lisada ei või.
    - Javaga kaasasolevaid sõnede otsimise meetodeid ei tohi kasutada.
      Test kontrollib, et teie koodis pole näiteks sõne "matches". Kui soovite ise
      sellise nimega meetodi teha, siis kasutage näiteks sõne "matchez" vms.