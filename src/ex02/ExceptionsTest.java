package ex02;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.sameInstance;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.fail;

public class ExceptionsTest {

    @Points(3)
    @Test(expected = MyException2.class)
    public void methodReadDataThrowsCorrectExceptions() throws Throwable {
        var exceptionThatFileReaderThrows = new RuntimeException();

        new Exceptions(new FileReader(exceptionThatFileReaderThrows)).readData();
    }

    @Test
    @Points(2)
    public void methodReadDataCanReadDataFromFileReader() throws Throwable {
        var randomData = String.valueOf(System.currentTimeMillis());
        var dataRead = new Exceptions(new FileReader(null, randomData)).readData();

        assertThat(dataRead, is(randomData));
    }

    @Test
    @Points(3)
    public void subThrowsExceptionWithCorrectRootCause() {
        var exceptionThatFileReaderThrows = new IllegalStateException();

        try {
            new Exceptions(new FileReader(exceptionThatFileReaderThrows)).sub();
        } catch (Exception e) {
            assertThat(getRootCause(e), sameInstance(exceptionThatFileReaderThrows));
            return;
        }

        fail();
    }

    @Test
    @Points(1)
    public void mainCanReadDataOriginatedFromFileReader() throws Throwable {
        var randomData = String.valueOf(System.currentTimeMillis());
        var dataRead = new Exceptions(new FileReader(null, randomData)).main();

        assertThat(dataRead, is(randomData));
    }

    @Points(3)
    @Test(expected = MyException3.class)
    public void mainThrowsCorrectException() throws Throwable {
        var exceptionThatFileReaderThrows = new IllegalArgumentException();

        new Exceptions(new FileReader(exceptionThatFileReaderThrows)).main();
    }

    @Test
    @Points(5)
    public void mainThrowsExceptionWithCorrectRootCause() {
        var exceptionThatFileReaderThrows = new IllegalArgumentException();

        try {
            new Exceptions(new FileReader(exceptionThatFileReaderThrows)).main();
        } catch (Throwable e) {
            assertThat(getRootCause(e), sameInstance(exceptionThatFileReaderThrows));
            return;
        }

        fail();
    }

    private Throwable getRootCause(Throwable t) {
        while (t.getCause() != null) {
            t = t.getCause();
        }

        return t;
    }
}
