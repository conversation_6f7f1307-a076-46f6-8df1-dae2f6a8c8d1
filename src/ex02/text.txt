valdav enamus tegid selle ära. Samas teooria oli väga nõrk.

Ülesanne 2 (17 punkti)

  Klass Exceptions ei tee midagi mõistlikku ja selle ainuke eesmärk on demonstreerida
  erindite (exception) p<PERSON><PERSON><PERSON><PERSON>, mähkimist (wrap) ja transleerimist. <PERSON><PERSON><PERSON><PERSON> vajalikud
  osad, et kood kompileeruks ja testid läheks läbi.

  Klass FileReader on abiklass, mis võimaldab juhtida, milline erind visatakse.

  Vajalik info on klassi Exceptions kommentaarides ja testides.

  PIIRANGUD!

    Muuta võite ainult klassi Exceptions meetodite sisu. Meetodite signatuure muuta ei või.

    <PERSON><PERSON><PERSON><PERSON><PERSON>, mis on klassi Exceptions kommentaarides.
