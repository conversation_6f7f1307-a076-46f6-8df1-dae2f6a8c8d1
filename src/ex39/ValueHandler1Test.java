package ex39;

import ex39.values.DoubleValue;
import ex39.values.IntegerValue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;
import runner.WarningChecker;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ValueHandler1Test {
    
    @Test
    @Points(2)
    public void canFilterTextValues() {
        List<IntegerValue> inputs = Arrays.asList(
                new IntegerValue(1),
                null,
                new IntegerValue(6),
                null);

        List<IntegerValue> result = ValueHandler1.removeNulls(inputs);

        assertThat(result.size()).isEqualTo(2);
        assertThat(result.getFirst().intValue()).isEqualTo(1);
        assertThat(result.getLast().intValue()).isEqualTo(6);
    }
    
    @Test
    @Points(3)
    public void canFilterIntegerValues() {
        List<DoubleValue> inputs = Arrays.asList(
                new DoubleValue(4.0),
                null,
                null,
                new DoubleValue(9.2),
                null);

        List<DoubleValue> result = ValueHandler1.removeNulls(inputs);

        assertThat(result.size()).isEqualTo(2);
        assertThat(result.getFirst().intValue()).isEqualTo(4);
        assertThat(result.getLast().intValue()).isEqualTo(9);
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotAddNewMethods() {
        Method[] methods = ValueHandler1.class.getDeclaredMethods();

        assertThat(methods.length).isEqualTo(1);
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldCompileWithoutWarnings() {
        assertThat(WarningChecker.hasWarnings(ValueHandler1.class)).isFalse();
        assertThat(WarningChecker.hasWarnings(ValueHandler1Test.class)).isFalse();
    }

}
