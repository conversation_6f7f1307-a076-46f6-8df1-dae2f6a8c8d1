package ex40;

public class MyDuration {

    public enum Unit { SECONDS, MINUTES, HOURS }

    private int totalSeconds;

    public MyDuration(int seconds) {
        this.totalSeconds = seconds;
    }

    public MyDuration(String timeString) {
        this.totalSeconds = parseTimeString(timeString);
    }

    private int parseTimeString(String timeString) {
        String[] timeParts = timeString.split(":");
        int hours = Integer.parseInt(timeParts[0]);
        int minutes = Integer.parseInt(timeParts[1]);
        int seconds = Integer.parseInt(timeParts[2]);

        return hours * 3600 + minutes * 60 + seconds;
    }

    public int get(Unit unit) {
        return switch (unit) {
            case HOURS -> (totalSeconds % 86400) / 3600;
            case MINUTES -> (totalSeconds % 3600) / 60;
            case SECONDS -> totalSeconds % 60;
        };
    }

    public MyDuration add(int value, Unit unit) {
        int additionalSeconds = switch (unit) {
            case HOURS -> value * 3600;
            case MINUTES -> value * 60;
            case SECONDS -> value;
        };
        return new MyDuration(this.totalSeconds + additionalSeconds);
    }

    public MyDuration add(MyDuration other) {
        return new MyDuration(totalSeconds + other.totalSeconds);
    }

    @Override
    public String toString() {
        int hours = (totalSeconds % 86400) / 3600;
        int minutes = (totalSeconds % 3600) / 60;
        int seconds = totalSeconds % 60;

        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MyDuration myTime = (MyDuration) obj;
        return totalSeconds == myTime.totalSeconds;
    }

    public MyDuration floor(Unit unit) {
        int days = totalSeconds / 86400;
        int hours = (totalSeconds % 86400) / 3600;

        if (unit == Unit.HOURS) {
            return new MyDuration(days * 86400 + hours * 3600);
        }

        return this;
    }
}
