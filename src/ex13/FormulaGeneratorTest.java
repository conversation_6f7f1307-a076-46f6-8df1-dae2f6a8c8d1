package ex13;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.lessThanOrEqualTo;

public class FormulaGeneratorTest {

    @Test
    @Points(7)
    public void integersAreAddedAndLongsMultiplied() {

        FormulaGenerator formulaStore = new FormulaGenerator();

        List<Formula> formulas = formulaStore.getFormulasFor(List.of(1, 2, 3));

        assertThat(formulas.size(), is(3)); // one formula object for each constant

        assertThat(formulas.get(0).compute(2), is(3));   // 2 + 1
        assertThat(formulas.get(0).compute(2L), is(2L)); // 2 * 1

        assertThat(formulas.get(1).compute(2), is(4));   // 2 + 2
        assertThat(formulas.get(1).compute(2L), is(4L)); // 2 * 2

        assertThat(formulas.get(2).compute(2), is(5));   // 2 + 3
        assertThat(formulas.get(2).compute(2L), is(6L)); // 2 * 3
    }

    @Test
    @Points(8)
    public void whenReversedLongsAreAddedAndIntegersMultiplied() {

        FormulaGenerator formulaStore = new FormulaGenerator();

        formulaStore.reverseFormulas();

        List<Formula> formulas = formulaStore.getFormulasFor(List.of(1, 2, 3));

        assertThat(formulas.size(), is(3)); // one formula object for each constant

        assertThat(formulas.get(0).compute(2), is(2));   // 2 * 1
        assertThat(formulas.get(0).compute(2L), is(3L)); // 2 + 1

        assertThat(formulas.get(1).compute(2), is(4));   // 2 * 2
        assertThat(formulas.get(1).compute(2L), is(4L)); // 2 + 2

        assertThat(formulas.get(2).compute(2), is(6));   // 2 * 3
        assertThat(formulas.get(2).compute(2L), is(5L)); // 2 + 3
    }

    @Test
    @NoPointsIfThisTestFails
    public void formulasShouldHaveOnlyOneIntegerField() {
        List.of(F1.class, F2.class, F3.class)
                .forEach(each ->
                    assertThat(getFieldCount(each), is(lessThanOrEqualTo(1))));

        int nonIntFieldCount = Stream.of(F1.class, F2.class, F3.class)
                .map(Class::getDeclaredFields)
                .flatMap(Arrays::stream)
                .filter(each -> each.getType().equals(int.class))
                .filter(each -> each.getType().equals(Integer.class))
                .toList().size();

        assertThat(nonIntFieldCount, is(0));
    }

    @Test
    @NoPointsIfThisTestFails
    public void formulaGeneratorShouldHaveOnlyOneBooleanField() {
        assertThat(getFieldCount(FormulaGenerator.class), is(lessThanOrEqualTo(1)));

        int nonBooleanFieldCount = Stream.of(FormulaGenerator.class.getDeclaredFields())
                .peek(each -> System.out.println(each.getType()))
                .filter(each -> !each.getType().equals(boolean.class))
                .filter(each -> !each.getType().equals(Boolean.class))
                .toList().size();

        assertThat(nonBooleanFieldCount, is(0));
    }

    public int getFieldCount(Class<?> clazz) {
        return clazz.getDeclaredFields().length;
    }
}
