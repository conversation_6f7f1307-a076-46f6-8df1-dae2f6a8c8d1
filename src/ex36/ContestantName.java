package ex36;

public class ContestantName {

    public final String value;

    public ContestantName(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ContestantName other)) {
            return false;
        }

        return this.value.equals(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }
}
