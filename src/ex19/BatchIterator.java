package ex19;

import java.util.Arrays;
import java.util.List;

public class BatchIterator {

    private List<Batch> batches;
    private int currentBatchIndex = 0;
    private int currentElementIndex = 0;

    public BatchIterator(Batch ... batches) {
        this.batches = Arrays.asList(batches);
    }

    public Batch nextBatch() {
        if (!hasNextBatch()) {
            throw new IllegalStateException("no more batches");
        }

        return batches.get(currentBatchIndex++);
    }

    public boolean hasNextBatch() {
        return currentBatchIndex < batches.size();
    }

    public Integer nextElement() {
        while (hasNextBatch()) {
            Batch currentBatch = batches.get(currentBatchIndex);

            if (currentElementIndex < currentBatch.getElements().size()) {
                return currentBatch.getElements().get(currentElementIndex++);
            }

            currentBatchIndex++;
            currentElementIndex = 0;
        }

        throw new IllegalStateException("no more elements");
    }
}
