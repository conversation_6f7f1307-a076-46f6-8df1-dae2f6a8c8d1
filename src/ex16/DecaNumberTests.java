package ex16;

import org.junit.Test;
import runner.Points;

import java.util.*;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class DecaNumberTests {

    private Set<DecaNumber> set = new LinkedHashSet<>();

    private List<DecaNumber> list = new ArrayList<>();

    @Test
    @Points(10)
    public void canTestWhetherListContainsDecaNumber() {

        list.add(new DecaNumber(10));

        assertThat(list.containsAll(toDecaNumbers(10, 13, 15, 19)),
                is(true));

        assertThat(list.contains(new DecaNumber(56)),
                is(false));

        list.add(new DecaNumber(55));

        assertThat(list.containsAll(toDecaNumbers(50, 53, 56, 59)),
                is(true));
    }

    @Test
    @Points(10)
    public void setDoesNotContainDuplicateDecaNumbers() {

        set.addAll(toDecaNumbers(11, 222, 53, 1000, 56, 227, 19));

        assertThat(set.toString(), is("[11, 222, 53, 1000]"));
    }

    private List<DecaNumber> toDecaNumbers(Integer ... numbers) {
        return Stream.of(numbers)
                .map(DecaNumber::new)
                .toList();
    }

}