<PERSON><PERSON><PERSON> (25 punkti)

  Ülesande kood asub Paketis ex1.

  <PERSON><PERSON> on testid välja kommenteeritud, kuna muidu
  need ei kompileeruks. Kui kirjutate klassides <PERSON>, SumFinder ja MaxFinder sobivad
  tüübid ja tüübiparameetrid, peaks ka testid kompileeruma. Seega on
  teie esmane ülesanne testid kompileerima saada.

  Testid on neljas failis: CoalesceTests.java, SumFinderTests1.java, SumFinderTests2.java
  ja MaxFinderTests.java.

  NB! Muuta võite klasse Util, SunFinder ja MaxFinder ning nendes klassides võite muuta
      olemasolevaid meetodeid ja klassiüleseid tüübiparameetreid. Piiranguks on see,
      et kui test sisse kommenteerida, siis peab kood kompileeruma. Uusi klasse või
      meetodeid luua ei tohi. Testides peate väljakommenteeritud koodi sisse kommenteerima.

  Util klassis on meetod coalesce(), mis võtab sisse kolm argumenti ja tagastab esimese,
  mis pole null. Kõik argumendid peavad olema sama tüüpi ja sama tüüpi on ka tagastatav väärtus.
  Kui ühtegi sellist pole, siis tagastatakse null. Selle testimiseks on CoalesceTests.java.

  Järgmistes ülesannetes on kasutusel klassid DecNumber ja HexNumber, mille mõlema ülemklass
  on MyNumber. Neid klasse pole vaja (ja ei tohi) muuta, ülesanded on nende kasutamise kohta.

  Klass SumFinder omab kahte summa arvutamise meetodit.

    a) Summa arvutatakse kahe argumendi põhjal. Töötav kood on välja kommenteeritud.
       Kui kirjutate korrektsed tüübi parameetrid, siis saate selle sisse kommenteerida.
       Selle osa kohta on test fails SumFinderTests1.java.

    b) Summa arvutatakse argumendina antud listi elementide summeerimise abil.
       Kommentaariga on antud vihje lahenduseks.
       Selle osa kohta on test failis SumFinderTests2.java.

  Klassi MaxFinder abil saab arvutada elementide listi maksimaalse väärtuse.
  Kirjutage sobivad tüübi parameetrid ja kood maksimaalse väärtuse leidmiseks.
  Selle osa kohta on test failis MaxFinderTests.java.
