package ex28;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class WordStore {

    private static final String[] order = {
            "one", "two", "three", "four", "five",
            "six", "seven", "eight", "nine", "ten"
    };

    private List<String> store = new ArrayList<>();

    public void add(String numberAsWord) {
        store.add(numberAsWord);
    }

    public List<String> getStoredWords() {
        return store.stream()
                .sorted(this::compare)
                .collect(Collectors.toList());
    }

    public int compare(String a, String b) {
        return getOrdinal(a).compareTo(getOrdinal(b));
    }

    private Integer getOrdinal(String numberAsWord) {
        return Arrays.asList(order).indexOf(numberAsWord);
    }
}
