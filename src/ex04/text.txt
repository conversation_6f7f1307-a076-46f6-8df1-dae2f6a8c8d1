3. eksam 13/49 0 punkti
3. eksam 13/49 max

Ülesanne 1 (20 punkti)

  Paketis ex1 on klass ScoreCounter ja test selle testimiseks.
  ScoreCounter peaks endas hoidma võistkonna liikmeid ja nende kogutud punkte.
  Võistkonna liikme infoks on eesnimi, perekonnanimi ja number.
  Võistkonna liikmed on kirjeldatud klassi ScoreCounter konstruktoris.

  Meetod addPoints() võimaldab lisada mingi arvu punkte konkreetsele võistkonna liikmele.
  Meetod addPoints() võtab argumendiks punktide arvu, eesnime, perekonnanime ja numbri
  ja lisab punktid vastavale liikmele.

  Meetodit addPoints() võib välja kutsuda ka nii, et mõni argument on null.
  Näiteks, kui perekonnanimi ja number on määratmata, siis otsitakse võistkonna
  liige eesnime järgi üles. Kui antud parameetritest ei piisa, et liiget üheselt
  tuvastada, siis peaks viskama MultipleMatchesException erindi. Kui antud
  parameetritele ei vasta ühtegi liiget, siis peaks viskama NoMatchException erindi.

  Lisaks on meetod getTeamPoints(), mis tagastab kõigi võistkonna liikmete punktide summa.

  PIIRANGUD!
    Klassil ScoreCounter võib olla ainult üks väli (instance variable).

    Muuta võite klassi ScoreCounter. Vajadusel võite uusi klasse juurde luua.
