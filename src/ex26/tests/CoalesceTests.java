package ex26.tests;

import ex26.Util;
import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.util.Arrays;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class CoalesceTests {

    @Test
    @Points(6)
    public void canCreateResultsOfFloats() {

        Integer result1 = Util.coalesce(1, 2, null);

        assertThat(result1, is(1));

        Double result2 = Util.coalesce(null, 2.0, 3.4);

        assertThat(result2, is(2.0));

        String result3 = Util.coalesce(null, null, "three");

        assertThat(result3, is("three"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotAddNewMethods() {
        long methodCount = Arrays.stream(Util.class.getDeclaredMethods()).count();

        assertThat(methodCount, is(1L));
    }

}
