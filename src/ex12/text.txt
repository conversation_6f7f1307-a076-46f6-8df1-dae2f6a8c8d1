32/41 maks punktidele
2/41 0 punkti

Ülesanne 1 (20 punkti)

  NB! Selle ülesande puhul ei ole lubatud mitmest osast koosnevat infot
          hoida sõne kujul.
          Nt. String step = "add" + "<<>>" + 4; ja pärast step.split("<<>>").
          Sõne kujul võime hoida infot fails või mujal välisel kandjal
          aga programmi sees nii infot ei hoita.

      Paketis ex1 on klass FormulaComposer ja test selle testimiseks.

      FormulaComposer-i abil saab koostada valemi, mis koosneb lihtsatest aritmeetilistest
      tehetest (sammudest).

      Kui FormulaComposer on loodud, siis saab sellele lisada kuitahes palju samme.

      Kui sammud on lisatud, saab valemit rakendada mingile numbrile (meetod applyTo()).

      Test canStoreAdditionStep() kirjeldab juhtu, kui luua<PERSON>e valem, mis koosneb
      ühest sammust (konstandi liitmine).

      Test canStoreSubtractionStep() kir<PERSON><PERSON>b juhtu, kui luua<PERSON>e valem, mis koosneb
      ühest sammust (konstandi lahutamine).

      Test canComposeFormulaStepByStep() kirjeldab juhtu, kui luuakse valem, mis koosneb
      mitmest sammust.

      Kirjutage vajalik kood, et testid tööle hakkaksid.

      Muuta võite klassi FormulaComposer. Vajadusel võite uusi klasse juurde luua.
