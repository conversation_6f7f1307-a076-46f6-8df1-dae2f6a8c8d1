package ex39;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

public class ValueHandler4<T> {

    private Map<Class<?>, Consumer<T>> handlers = new HashMap<>();

    public void registerHandler(Class<?> valueType,
                                Consumer<T> handler) {

        handlers.put(valueType, handler);
    }

    public void handle(T value) {
        Consumer<T> handler = handlers.get(value.getClass());
        if (handler != null) {
            handler.accept(value);
        }
    }
}
