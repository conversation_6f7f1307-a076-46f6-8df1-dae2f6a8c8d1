package runner;

import javax.tools.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class WarningChecker {

    public static boolean hasWarnings(Class<?> clazz) {
        String pathAsString = String.format("src/%s.java",
                clazz.getName().replaceAll("\\.", "/"));

        Path path = Paths.get(pathAsString);

        String sourceCode;
        try {
            sourceCode = Files.readString(path);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String fileName = path.getFileName().toString();
        String className = fileName.endsWith(".java") ?
                fileName.substring(0, fileName.length() - 5) : fileName;

        return hasWarnings(sourceCode, className);
    }

    private static boolean hasWarnings(String sourceCode, String className) {
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        if (compiler == null) {
            throw new RuntimeException("No compiler available. Make sure you're running with JDK, not JRE");
        }

        JavaFileObject sourceFile = new StringJavaFileObject(className, sourceCode);

        var fileManager = new InMemoryFileManager(compiler.getStandardFileManager(null, null, null));
        DiagnosticCollector<JavaFileObject> diagnostics = new DiagnosticCollector<>();

        List<String> options = List.of("-Xlint:all");

        JavaCompiler.CompilationTask task = compiler.getTask(
                null,
                fileManager,
                diagnostics,
                options,
                null,
                List.of(sourceFile)
        );

        task.call();

        for (Diagnostic<? extends JavaFileObject> diagnostic : diagnostics.getDiagnostics()) {
            if (diagnostic.getKind() == Diagnostic.Kind.WARNING) {
                System.out.println("Warning: " + diagnostic.getMessage(null));
                return true;
            }
        }

        return false;
    }

    static class StringJavaFileObject extends SimpleJavaFileObject {
        private final String code;

        StringJavaFileObject(String name, String code) {
            super(java.net.URI.create("string:///" + name.replace('.', '/') + Kind.SOURCE.extension), Kind.SOURCE);
            this.code = code;
        }

        @Override
        public CharSequence getCharContent(boolean ignoreEncodingErrors) {
            return code;
        }
    }

    static class InMemoryFileManager extends ForwardingJavaFileManager<StandardJavaFileManager> {
        InMemoryFileManager(StandardJavaFileManager fileManager) {
            super(fileManager);
        }

        @Override
        public JavaFileObject getJavaFileForOutput(Location location, String className,
                                                   JavaFileObject.Kind kind, FileObject sibling) throws IOException {
            if (kind == JavaFileObject.Kind.CLASS) {
                return new ByteArrayJavaFileObject(className);
            }
            return super.getJavaFileForOutput(location, className, kind, sibling);
        }
    }

    static class ByteArrayJavaFileObject extends SimpleJavaFileObject {

        ByteArrayJavaFileObject(String className) {
            super(URI.create("memory:///" + className.replace('.', '/') + Kind.CLASS.extension), Kind.CLASS);
        }

        @Override
        public OutputStream openOutputStream() {
            return new ByteArrayOutputStream();
        }
    }
}