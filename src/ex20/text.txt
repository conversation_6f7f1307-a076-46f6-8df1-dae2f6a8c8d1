  Ülesanne 1 (25 punkti)

  Paketis ex1 on klass ArrayDeque ja testid selle käitumise kontrollimiseks.
  Deque on järjestust hoidev andmes<PERSON><PERSON><PERSON>ur, mis võimaldab lisada
  elemente nii algusesse kui lõppu. Ka elementide eemaldamine on võimalik
  mõlemast otsast. Klass ArrayDeque kasutab elementide hoidmiseks massiivi.

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.
      Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
      kirjutada alguses vaid kood, mis esimese testi läbib jne.

  Klassi ArrayDeque konstruktoris antakse ette selle maksimaalne maht. Pole vaja
  arvestada juhuga, kus algsest mahust väheks jääb.

  Meetod addLast() lisab elemendi järjekorra lõppu ja meetod removeLast() eemaldab
  elemendi järjekorra lõpust ning tagastab selle. Se<PERSON> osa testimiseks on test
  elementsCanBeAddedToTheEnd().

  Meetod getContents() tagastab kõik lisatud elementid õiges järjekorras. Selle osa
  testimiseks on test canSeeTheContentsOfDeque().

  Meetod removeFirst() eemaldab elemendi algusest ja tagastab selle.
  Selle osa testimiseks on test elementsCanBeRemovedFromTheBeginning().

  Meetod addFirst() lisab elemendi algusesse. Selle osa testimiseks on
  meetod elementsCanBeAddedToTheBeginning().

  Kui järjekord on tühi, siis viskab elemendi eemaldamise meetod erindi IllegalStateException.
  Seda osa kontrollib test throwsWhenNoMoreElements().

  Meetod getSortedContents() tagastab lisatud elemendid sorteeritud kujul.
  Seda osa kontrollib test canGetElementsInSorterOrder().

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi ArrayDeque ja uusi klasse lisada ei või.
    - Klasse paketist java.util ja meetodit System.arraycopy kasutada ei tohi.
      Vajaliku koodi peate ise kirjutama (nt. for tsükkel).
