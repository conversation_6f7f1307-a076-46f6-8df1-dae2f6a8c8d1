Propose an exercise for Java course exam. It should test the simple algorithmic thinking.
One sample exercise from previous jear is provided as an example.
Students have access to exercises from previous years so new exercise
should be different enough that the solution can’t be just copied.

This is an introductory course and students have only on hour to complete the solution.

The exercise should be structured so that weak students get some points
if they have even minimal understanding of the material. In the example
the tests are in the order of rising complexity.

Provide just the idea of the new exercise for first review.

Do not add comments.
Do not provide both solution and initial state. Solution is enough.
