package ex24;

public class DimensionStore {

    public static final int X_DIMENSION = 4;
    public static final int Y_DIMENSION = 3;
    public static final int Z_DIMENSION = 2;

    private int[] elements1d = new int[X_DIMENSION];

    private int[][] elements2d = new int[Y_DIMENSION][X_DIMENSION];

    private int[][][] elements3d = new int[Z_DIMENSION][Y_DIMENSION][X_DIMENSION];

    private int x = 0;
    private int y = 0;
    private int z = 0;

    public void add(int value) {
        elements3d[z][y][x] = value;

        x++;

        if (x == X_DIMENSION) {
            x = 0;
            y++;
        }

        if (y == Y_DIMENSION) {
            y = 0;
            z++;
        }
    }

    public int size() {
        return z * Y_DIMENSION * X_DIMENSION + y * X_DIMENSION + x;
    }

    public int[] values() {
        int[] result = new int[size()];
        int count = 0;
        for (int i = 0; i < Z_DIMENSION; i++) {
            for (int j = 0; j < Y_DIMENSION; j++) {
                for (int k = 0; k < X_DIMENSION; k++) {
                    result[count++] = elements3d[i][j][k];
                    if (count == size()) {
                        return result;
                    }
                }
            }
        }

        return result;
    }

    public int shift() {
        if (size() == 0) {
            throw new IllegalStateException("store is empty");
        }

        int[] values = values();
        x = 0;
        y = 0;
        z = 0;

        int count = 0;
        for (Integer value : values) {
            if (count++ == 0) {
                continue;
            }
            add(value);
        }

        return values[0];
    }

    public int getLast() {
        if (size() == 0) {
            throw new IllegalStateException("store is empty");
        }

        return values()[size() - 1];
    }

    public void addAll(int[] values) {
        for (int each : values) {
            add(each);
        }
    }
}
