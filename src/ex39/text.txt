<PERSON><PERSON>anne (25 punkti)

  NB! Ülesande piirangud on teks<PERSON> lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Ülesande kood asub Paketis ex2.

  Se<PERSON> ülesandes on osa testide koodi välja kommenteeritud, kuna muidu
  need ei kompileeruks. Kui kirjutate klassides ValueHandler1 - ValueHandler5
  sobivad tüübid ja tüübiparameetrid, peaks ka testid kompileeruma. Seega on
  teie esmane ülesanne testid kompileerima saada.

  Testid on failides ValueHandler1Test.java - ValueHandler5Test.java.

  ValueHandler1.removeNulls() eemaldab etteantud listist null elemendid.

  ValueHandler2.filter() eemaldab etteantud listist tigimusele mittevastavad elemendid.

  ValueHandler3.findLargest() leiab suurima väärtuse.

  ValueHandler4.registerHandler(klass, handler) registreerib töötleja konkreetset tüüpi
                                                väärtuste jaoks.

  ValueHandler4.handle(väärtus) - käivitab antud tüübile registreeritud töötleja.

  ValueHandler5.registerHandler(klass, handler) sama, mis ValueHandler4.registerHandler

  ValueHandler5.handle(väärtused) - käivitab antud tüübile registreeritud töötleja iga
                                    etteantud väärtuse kohta.

  NB! - Muuta võite klasse ValueHandler1 - ValueHandler5 aga
        nendesse ei või lisada uusi meetodeid.
      - Uusi faile luua ei või.
      - Kui testidest väljakommenteeritud osa sisse kommenteerida
        peab kood hoiatusteta kompileeruma.
      - Koodis ei tohi olla instanceof operaatorit või cast-i.
