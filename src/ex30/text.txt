Ülesanne 1 (25 punkti)

  NB! Teksti lõpus on tingimused, mille eiramisel ei lähe tulemus arvesse.

  Kataloogis ex1 on klass (record) MyDate, mis kujutab kuupäeva ja klass
  DiffCalculator, mis võimaldab arvutada päevade arvu kahe kuupäeva vahel.
  Näiteks 22. mai ja 24. mai vah<PERSON> on 2 päeva.

  Kuude pikkused on toodud v<PERSON>l<PERSON>l "lengths" olevas massiivis. Veebruari pikkuseks
  on seal märgitud 28 päeva aga sõltuvalt aastast võib see olla ka 29. <PERSON><PERSON> isLeapYear(<year>)
  on teie eest ära tehtud ja ütleb, kas sellel aastal on veebruaris 29 päeva.

  Esimeste testide läbimiseks ei pea liigaastaga (leap year) arvestama.

  NB! Tingimiused, mille eiramisel ei lähe tulemus arvesse.
    - Muuta võite klassi DiffCalculator.java. Uusi klasse luua ei tohi.
    - Klasse paketist java.time või java.util kasutada ei või.
      Ka koodi nendest klassidest kopeerida ei või.
    - Kood peab läbima testid klassist DiffCalculatorTest.
