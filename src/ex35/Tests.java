package ex35;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.Points;
import runner.PointsCounterExtension;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class Tests {

    @Test
    @Points(2)
    public void findsSumOfIntegers() {
        assertThat(Code.sum(List.of(1, 2, 5, 8))).isEqualTo(16);
    }

    @Test
    @Points(5)
    public void findsFirstMatchingElement() {
        List<Integer> list = List.of(1, 2, 5, 8);
        List<String> list2 = List.of("12", "233", "1");

        assertThat(Code.findFirst(list, (Integer x) -> x > 2).get()).isEqualTo(5);

        assertThat(Code.findFirst(list2, (String x) -> x.length() > 2).get()).isEqualTo("233");

        assertThat(Code.findFirst(list, (Integer x) -> x < 1).isEmpty()).isTrue();
    }

    @Test
    @Points(5)
    public void retriesProvidedOperation() {

        int failuresBeforeSuccess = 0;
        int totalAttempts = 1;

        boolean result = Code.retry(new Job(failuresBeforeSuccess), totalAttempts);

        assertThat(result).isTrue();

        assertThat(Code.retry(new Job(1), 1)).isFalse();

        assertThat(Code.retry(new Job(1), 2)).isTrue();

        assertThat(Code.retry(new Job(2), 2)).isFalse();

        assertThat(Code.retry(new Job(2), 3)).isTrue();
    }

    public void flattensWithReduce() {
        List<List<Integer>> lists = List.of(
                List.of(3, 5),
                List.of(7, 8),
                List.of(),
                List.of(1, 4)
        );

        List<Integer> flattened = Code.flatten(lists);

        assertThat(flattened).containsExactly(3, 5, 7, 8, 1, 4);
    }

    @Test
    @Points(6)
    public void sortsByMultipleCriteria() {
        List<String> words = List.of(
                "acquittances", "accedence", "accomplishing",
                "acerbated", "adagios");

        Code code = new Code();

        List<String> sorted = code.sortByCFrequency(words);

        assertThat(sorted).containsExactly(
                "adagios", "acerbated", "accomplishing",
                "acquittances", "accedence");

        assertThat(code.callCount).isEqualTo(words.size());
    }

    @Test
    @Points(7)
    public void createsMapByFirstLetter() {
        Map<Character, List<String>> actual = Code.toMap(List.of(
                "Ant", "Ape", "Bear", "Bee", "Bat", "Cat", "Cow"));

        Map<Character, List<String>> expected = Map.of(
                'A', List.of("Ant", "Ape"),
                'B', List.of("Bear", "Bee", "Bat"),
                'C', List.of("Cat", "Cow"));

        assertThat(actual).containsExactlyInAnyOrderEntriesOf(expected);
    }

}