package ex12;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class FormulaComposerTest {

    @Test
    @Points(3)
    public void canStoreAdditionStep() {

        FormulaComposer composer = new FormulaComposer();

        composer.add(5);

        assertThat(composer.applyTo(4), is(9)); // 4 + 5
    }

    @Test
    @Points(6)
    public void canStoreSubtractionStep() {

        FormulaComposer composer = new FormulaComposer();

        composer.subtract(7);

        assertThat(composer.applyTo(3), is(-4)); // 3 - 7
    }

    @Test
    @Points(8)
    public void canComposeFormulaStepByStep() {

        FormulaComposer composer = new FormulaComposer();

        composer.subtract(3);
        composer.add(9);
        composer.multiply(2);

        assertThat(composer.applyTo(3), is(18)); // (3 - 3 + 9) * 2

        // order can change

        composer = new FormulaComposer();

        composer.subtract(3);
        composer.multiply(2);
        composer.subtract(4);
        composer.add(9);
        composer.multiply(3);

        assertThat(composer.applyTo(5), is(27)); // (((5 - 3) * 2) - 4 + 9) * 3
    }
}
