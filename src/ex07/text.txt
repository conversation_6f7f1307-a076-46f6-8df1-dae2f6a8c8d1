Ülesanne 1 (15 punkti)

  Kataloogis ex1 on klass Board ja test selle testimiseks.

  Board on klass, mis kujutab ruutudest koosnevat tabelit. Tabelil on read ja veerud.

  new Board(<ridade arv>, <veergude arv>); loob uue tabeli.

  Meetod set(<rida>, <veerg>) märgistab parameetritega etteantud ruudu.

  Meetod isSet(<rida>, <veerg>) ütleb, kas parameetritega etteantud ruut on märgistatud.

  Kui parameetritele vastavat ruutu pole olemas, siis visatakse IllegalArgumentException.

  Meetod isIsolated(<rida>, <veerg>) ütleb, kas parameetritega etteantud ruut või tema
  lähinaabruses olev ruut on märgistatud.

  Näide (3x6 tabel):

     -nnn--
     -nXn--
     -nnn--

     "-" märgistamata
     "X" märgistatud
     "n" märgistatud ruudu lähinaabrus

  Kirjutage vajalik kood, et testid tööle hakkaksid.

  Muuta võite klassi Board.java. Uusi klasse luua ei tohi.
