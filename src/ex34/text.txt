  Ülesanne 1 (25 punkti)

  NB! Ülesande piirangud on teks<PERSON> lõpus. Nende eiramisel tulemus arvesse ei lähe.
      Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
      kirjutada alguses vaid kood, mis esimese testi läbib jne.

  Paketis ex1 on klass MyArrayList ja testid selle käitumise kontrollimiseks.
  MyArrayList on lihtne versioon numbrite listist, mis võimaldab elemente lisada ja
  eemaldada. Elementide hoidmiseks peab kasutama massiivi. Konstruktoris antakse ette selle
  maksimaalne maht. Pole vaja arvestada juhuga, kus algsest mahust väheks jääb.

  MyArrayList käitumine on järgmine.

  add() - lisab elemendi listi lõppu.

  removeLast() - eemaldab elemendi listi lõust ja tagastab selle.

  getElements() - tagastab kõik lisatud elemendid massiivina.

  reverse() - pöörab ringi elementide järjestuse.

  removeFirst() - eemaldab esimese elemendi.

  add(<index>, <element>) - lisab elemendi indeksiga määratud kohale.

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi MyArrayList ja uusi faile lisada ei või.
    - Klasse paketist java.util ja meetodit System.arraycopy kasutada ei tohi.
      Vajaliku koodi peate ise kirjutama (nt. for tsükkel).
