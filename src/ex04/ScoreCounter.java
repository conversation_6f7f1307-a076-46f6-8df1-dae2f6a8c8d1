package ex04;

import java.util.ArrayList;
import java.util.List;

public class ScoreCounter {

    private List<Entry> people = new ArrayList<>();

    public ScoreCounter() {

        String data = "<PERSON>,<PERSON><PERSON><PERSON>,12\n" +
                "<PERSON>,<PERSON><PERSON><PERSON>,3\n" +
                "<PERSON><PERSON>,<PERSON>,15\n" +
                "<PERSON>,<PERSON><PERSON>,5\n" +
                "<PERSON>,<PERSON>,18\n" +
                "<PERSON>,<PERSON><PERSON>,14\n" +
                "<PERSON>,<PERSON><PERSON><PERSON>,1\n" +
                "<PERSON>,<PERSON><PERSON>,2\n" +
                "<PERSON>,<PERSON><PERSON>,13\n" +
                "<PERSON>,<PERSON>,10\n" +
                "<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,4\n" +
                "<PERSON>,<PERSON>,8\n" +
                "<PERSON><PERSON>,<PERSON>,16\n" +
                "<PERSON><PERSON><PERSON>,<PERSON>,17\n" +
                "<PERSON>,<PERSON><PERSON><PERSON>,11\n" +
                "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,7\n" +
                "<PERSON>,<PERSON>,9\n" +
                "<PERSON><PERSON>,<PERSON>,6\n" +
                "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,19\n";

        for (String line : data.split("\n")) {
            String firstName = line.split(",")[0];
            String lastName = line.split(",")[1];
            Integer number = Integer.parseInt(line.split(",")[2]);

            people.add(new Entry(firstName, lastName, number));
        }

    }

    public void addPoints(int points, String firstName,
                          String lastName, Integer number) {

        List<Entry> found = people.stream()
                .filter(p -> firstName == null || p.firstName.equals(firstName))
                .filter(p -> lastName == null || p.lastName.equals(lastName))
                .filter(p -> number == null || p.number.equals(number))
                .toList();

        if (found.size() < 1) {
            throw new NoMatchException();
        } else if (found.size() > 1) {
            throw new MultipleMatchesException();
        } else {
            found.get(0).points += points;
        }

    }

    public int getPoints(int number) {
        return people.stream()
                .filter(p -> p.number.equals(number))
                .findFirst().get().points;
    }

    public int getTeamPoints() {
        return people.stream()
                .mapToInt(p -> p.points)
                .sum();
    }
}
