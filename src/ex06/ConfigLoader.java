package ex06;

import java.io.Closeable;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

public class ConfigLoader {

    private Properties properties = new Properties();

    public ConfigLoader(String filePath) {

        MyFileInputStream is = null;

        try {
            is = new MyFileInputStream(filePath);

            InputStreamReader reader = new InputStreamReader(is, StandardCharsets.UTF_8);

            properties.load(reader);
        } catch (FileNotFoundException e) {
            throw new Exception2(e);
        } catch (IOException e) {
            throw new Exception3(e);
        } finally {
            close(is);
        }
    }

    private void close(Closeable resource) {
        if (resource == null) {
            return;
        }

        try {
            resource.close();
        } catch (IOException ignore) {}
    }

    public String getValue(String key) {
        if (!properties.containsKey(key)) {
            throw new Exception1();
        }

        return properties.getProperty(key);
    }
}
