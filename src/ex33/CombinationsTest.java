package ex33;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.Points;
import runner.PointsCounterExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;

@ExtendWith(PointsCounterExtension.class)
public class CombinationsTest {

    @Test
    @Points(2)
    public void canHoldSmallValues() {
        Combinations number = new Combinations("3");

        System.out.println(number.asInt());

    }

}