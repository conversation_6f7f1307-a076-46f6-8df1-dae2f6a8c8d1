package ex05;

// java.util

public class NumberStore {
    private int[] data = new int[100];

    private int size = 0;

    public void add(int element) {
        int pos = findPos(element);
        shiftElementsFrom(pos);
        data[pos] = element;
        size++;
    }

    private int findPos(int element) {
        for (int i = 0; i < size; i++) {
            if (data[i] > element) {
                return i;
            }
        }

        return size;
    }

    private void shiftElementsFrom(int pos) {
        for (int i = data.length - 2; i >= pos; i--) {
            data[i + 1] = data[i];
        }
    }

    public int[] getStoredNumbers() {
        int[] temp = new int[size];
        for (int i = 0; i < size; i++) {
            temp[i] = data[i];
        }

        return temp;
    }
}
