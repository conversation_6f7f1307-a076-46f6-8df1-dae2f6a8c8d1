package ex36;

import java.util.List;

public record Contestant(ContestantName contestantName) {

    public static final List<String> TEAM1 = List.of("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>");
    public static final List<String> TEAM2 = List.of("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>");
    public static final List<String> TEAM3 = List.of("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>");
    public static final List<String> TEAM4 = List.of("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>");

    private List<String> findMyTeam(String name) {
        List<List<String>> teams = List.of(TEAM1, TEAM2, TEAM3, TEAM4);

        for (List<String> team : teams) {
            if (team.contains(name)) {
                return team;
            }
        }

        throw new RuntimeException("contestant not found");
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Contestant other)) {
            return false;
        }

        return findMyTeam(contestantName.value)
                .contains(other.contestantName.value);
    }

    @Override
    public int hashCode() {
        return 1;
    }

    @Override
    public String toString() {
        return contestantName.value;
    }
}
