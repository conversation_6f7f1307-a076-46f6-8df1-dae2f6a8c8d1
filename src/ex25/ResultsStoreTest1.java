package ex25;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;

public class ResultsStoreTest1 {

    @Test
    @Points(8)
    public void resultsDictWorksWithDifferentTypesOfKeys() {

        // Keys of String type

        ResultsStore<String, Integer> store = new ResultsStore<>();

        store.addResult("Alice", new Results<>(2, 9, 0));
        store.addResult("Bob", new Results<>(0, 2, 1));

        assertThat(store.getResults("Alice"), contains(2, 9, 0));
        assertThat(store.getResults("Bob"), contains(0, 2, 1));

        // Keys of Person type

        Person alice = new Person("Carol");

        ResultsStore<Person, Integer> store2 = new ResultsStore<>();

        store2.addResult(alice, new Results<>(4, 3, 0));

        assertThat(store2.getResults(alice), contains(4, 3, 0));
    }

    private record Person(String name) {}
}
