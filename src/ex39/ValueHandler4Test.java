package ex39;

import ex39.values.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;
import runner.WarningChecker;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ValueHandler4Test {

    @Test
    @Points(4)
    public void canRegisterHandlers() {
        List<NumericValue> integers = new ArrayList<>();
        List<NumericValue> doubles = new ArrayList<>();

        ValueHandler4<NumericValue> handler = new ValueHandler4<>();

        handler.registerHandler(IntegerValue.class, integers::add);
        handler.registerHandler(DoubleValue.class, doubles::add);

        handler.handle(new DoubleValue(1.0));
        handler.handle(new IntegerValue(6));
        handler.handle(new DoubleValue(5.5));

        assertThat(integers.toString()).isEqualTo("[6]");
        assertThat(doubles.toString()).isEqualTo("[1.0, 5.5]");
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldCompileWithoutWarnings() {
        assertThat(WarningChecker.hasWarnings(ValueHandler4.class)).isFalse();
        assertThat(WarningChecker.hasWarnings(ValueHandler4Test.class)).isFalse();
    }

}
