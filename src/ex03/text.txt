2. eksam. 9/41 0 punkti
          20/41 max

tehti väga keeruliselt. tsüklid ja asjad

<PERSON> 1 (20 punkti)

  Odometer on kilomeetrite lugeja. See koosneb tavaliselt ratastest, millel on
  numbrid. Iga kilomeetri järel keeratakse paremal olevat ratast ühe numbri võrra
  edasi. Kui esimene ratas on teinud täispöörde, siis keeratakse teist ratast
  ühe võrra edasi. <PERSON><PERSON> kõ<PERSON> rattad on täispöörde teinud hakkab kogu asi algusest pihta.
  Analoogiliselt töötavad näiteks ka sammulugeja või veemõõtja.

  Peaksite kirjutama koodi, mis seda käitumist simuleerib. Erinevus on see, et
  antud juhul ei ole ratastel numbrid 0-9 vaid tähed A, B ja C.

  Paketis ex1 on klass Odometer ja test selle testimiseks.
  Meetod getReading() tagastab hetke näidu sõne kujul. Algnäit on "AAA".
  Meetod click() liigutab loendurit ühe võrra edasi.

  Teie kood võib arvesada, et loenduril on kolm ratast ja uue ratta lisamine ei
  pea tingimata lihtne olema. Küll aga peaks olema lihtne ratta suuruse muutmine.
  Kui soovime, et A, B ja C asemel oleks A, B, C, D, E, F, jne, peaks see muudatus
  väga lihtsalt tehtav olema.

  PIIRANGUD!
    Loomulikult ei tohi tagastatavaid näite lihtsalt programmi sisse kirjutada.

    Muuta võite vaid klassi Odometer. Uusi klasse luua ei või.
