package ex09;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class UniqueNameFinder {

    private List<Person> persons = new ArrayList<>();

    public void addPerson(Person person) {
        persons.add(person);
    }

    public boolean containsPersonWithTheSameName(Person person) {
        return persons.contains(person);
    }

    public List<Person> personsWithUniqueNames() {
        List<Person> result = new ArrayList<>();
        for (Person person : persons) {
            if (Collections.frequency(persons, person) == 1) {
                result.add(person);
            }
        }

        return result;
    }
}
