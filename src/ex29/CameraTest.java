package ex29;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.sameInstance;

public class CameraTest {

    @Test
    @Points(3)
    public void canMoveHorizontally() {
        Camera camera = new Camera();

        camera.move(Axes.HORIZONTAL, Direction.CLOCKWISE, 1);

        assertPosition(camera, 1, 0);

        camera.move(Axes.HORIZONTAL, Direction.CLOCKWISE, 2);

        assertPosition(camera, 3, 0);

        camera.move(Axes.HORIZONTAL, Direction.COUNTERCLOCKWISE, 6);

        assertPosition(camera, -3, 0);
    }

    @Test
    @Points(3)
    public void canMoveVertically() {
        Camera camera = new Camera();

        camera.move(Axes.VERTICAL, Direction.CLOCKWISE, 1);

        assertPosition(camera, 0, 1);

        camera.move(Axes.VERTICAL, Direction.CLOCKWISE, 4);

        assertPosition(camera, 0, 5);

        camera.move(Axes.VERTICAL, Direction.COUNTERCLOCKWISE, 2);

        assertPosition(camera, 0, 3);
    }

    @Test
    @Points(8)
    public void canCancelSingleOperation() {
        Camera camera = new Camera();

        camera.move(Axes.HORIZONTAL, Direction.CLOCKWISE, 1);
        camera.move(Axes.VERTICAL, Direction.COUNTERCLOCKWISE, 3);

        assertPosition(camera, 1, -3);

        camera.cancelLastAction();

        assertPosition(camera, 1, 0);
    }

    @Test
    @Points(11)
    public void canCancelMultipleOperations() {
        Camera camera = new Camera();

        camera.move(Axes.HORIZONTAL, Direction.CLOCKWISE, 1);
        camera.move(Axes.VERTICAL, Direction.COUNTERCLOCKWISE, 3);
        camera.move(Axes.VERTICAL, Direction.COUNTERCLOCKWISE, 4);
        camera.move(Axes.HORIZONTAL, Direction.COUNTERCLOCKWISE, 3);

        assertPosition(camera, -2, -7);

        camera.cancelLastAction();
        assertPosition(camera, 1, -7);

        camera.cancelLastAction();
        assertPosition(camera, 1, -3);

        camera.cancelLastAction();
        assertPosition(camera, 1, 0);
    }

    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() {
        Field[] fields = Camera.class.getDeclaredFields();

        assertThat(fields.length, is(3));

        assertThat(fields[0].getType(), sameInstance(Integer.TYPE));
        assertThat(fields[1].getType(), sameInstance(Integer.TYPE));

        assertThat(fields[2].getGenericType().getTypeName(),
                is("java.util.LinkedList<java.lang.Runnable>"));
    }

    private void assertPosition(Camera robot, int x, int y) {
        assertThat("Horizontal position should be " + x,
                robot.getPosition(Axes.HORIZONTAL), is(x));
        assertThat("Vertical position should be " + y,
                robot.getPosition(Axes.VERTICAL), is(y));
    }
}