package ex07;

public class Board {

    private boolean[][] board;

    public Board(int rows, int columns) {
        board = new boolean[rows][columns];
    }

    public void set(int row, int column) {
        ensureValidPosition(row, column);

        board[row - 1][column - 1] = true;
    }

    private void ensureValidPosition(int row, int column) {
        if (row < 1
                || column < 1
                || row > board.length
                || column > board[0].length) {
            throw new IllegalArgumentException();
        }
    }

    public boolean isSet(int row, int column) {
        ensureValidPosition(row, column);

        return board[row - 1][column - 1];
    }

    public boolean isIsolated(int row, int column) {
        int rowStart = Math.max(0, row - 2);
        int rowEnd = Math.min(row, board.length - 1);
        int colStart = Math.max(0, column - 2);
        int colEnd = Math.min(column, board[0].length - 1);

        for (int r = rowStart; r <= rowEnd; r++) {
            for (int c = colStart; c <= colEnd; c++) {
                if (board[r][c]) {
                    return false;
                }
            }
        }

        return true;
    }
}
