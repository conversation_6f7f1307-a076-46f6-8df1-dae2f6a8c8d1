------------------- test1 a ----------------------------

Ex: ArrayMap.java

Kui allolev kood kompileerub, siis mis viga saab juhtuda real 2.
     1. long x = ...;
     2. long y = (int) x;

2. <PERSON><PERSON> on erindid E1 ja E2:
       public class E1 extends RuntimeException {}
       public class E2 extends Exception {}

------------------- test1 b ----------------------------

Ex: DimensionStore.java

Kui allolev kood kompileerub, siis MIS VIGA saab juhtuda real 2.
     1. long x = ...;
     2. float y = x;

4. Mis on alloleva koodi MÕTE (eesmärk)?

     try {
         ...
     } catch (IOException e) {
         throw new RuntimeException(e);
     }

------------------- test2 a ----------------------------

Ex: ResultsStore.java

Mis on abstraktse meetodi mõte?
Mis kasu on enum tüüpidest?
System.out.println(set.size()); hashCode()

------------------- test2 b ----------------------------

Ex: MaxFinder.java, SumFinder.java, Util.coalesce()

"is a" suhe
new ArrayList<>(new HashSet<>(input));
catch (IndexOutOfBoundsException e)

------------------- exam 1 ----------------------------

Ex1: ArrayDeque
Ex2: ScientificNumber

Mis probleem võib tekkida primitiivide cast-imisel?
Mis on Javas erindite mähkimise (wrapping) eesmärk?
Objekt aitab varjata teostuse detaile. Miks peaks midagi varjama?
Tooge kaks näidet, milleks Java refleksiooni kasutada.

  2. Meil on vaja hoida infot erakondade ja nende valimistulemuste kohta.
       REF => 28,9
       KESK => 23,1
       EKRE => 17,8

     Selleks sobiks sõnastik aga see oleks lühinägelik lahendus. Miks?

------------------- exam 2 ----------------------------
Ex1: DateDiff
Ex2: Camera

Ujuvkoma arvud, ei sobi finantsarvutuste tegemiseks. Miks?
Mis asi on objekti VAIKIMISI (default) konstruktor?
Mis kasu on enum tüüpidest?
Mis on Javas geneeriliste tüüpide tüübi piirangute (bounded type parameters) eesmärk?
Mida tähendab allolevas koodis "static"?

  import static org.hamcrest.CoreMatchers.is;

------------------- exam 3 ----------------------------

Ex1: Board.getChunkSize()
Ex2: Camera

Float.MAX_VALUE == Float.MAX_VALUE - 1. Miks?
Mis on Javas liidese ja abstraktse klassi vahe?
Miks eelistada Enum tüüpi konstante sõne tüüpi konstantidele?

Mis on alloleva koodi mõte?

try {
    // read translation file
} catch (IOException e) {
    throw new MissingLanguageFileException(e);
}

Mida kirjutada allolevas koodis tärini asemele? 1p Mida see tähendab? 4p.
class {
    public List<* extends Number> min() {

    }
}
