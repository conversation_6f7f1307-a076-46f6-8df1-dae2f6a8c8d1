package ex41;

public class ArrayTraverse {

    public static boolean canBeTraversed(int[] array) {
        return canBeTraversed(array, false);
    }

    public static boolean canBeTraversed(int[] array, boolean biDirectionalSteps) {
        if (array == null || array.length == 0) {
            return false;
        }

        if (array.length == 1) {
            return true;
        }

        int targetIndex = array.length - 1;

        if (biDirectionalSteps) {
            return canReachTarget(array, 0, targetIndex, new boolean[array.length]);
        } else {
            int currentIndex = 0;
            boolean[] visited = new boolean[array.length];

            while (currentIndex >= 0 && currentIndex < array.length) {
                if (currentIndex == targetIndex) {
                    return true;
                }

                if (visited[currentIndex]) {
                    return false;
                }

                visited[currentIndex] = true;
                int step = array[currentIndex];
                currentIndex += step;
            }

            return false;
        }
    }

    private static boolean canReachTarget(int[] array, int currentIndex, int targetIndex, boolean[] visited) {
        if (currentIndex < 0 || currentIndex >= array.length) {
            return false;
        }

        if (currentIndex == targetIndex) {
            return true;
        }

        if (visited[currentIndex]) {
            return false;
        }

        visited[currentIndex] = true;
        int step = array[currentIndex];

        boolean result = canReachTarget(array, currentIndex + step, targetIndex, visited) ||
                        canReachTarget(array, currentIndex - step, targetIndex, visited);

        visited[currentIndex] = false;
        return result;
    }

}
