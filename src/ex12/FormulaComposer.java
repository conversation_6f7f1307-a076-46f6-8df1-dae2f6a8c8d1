package ex12;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class FormulaComposer {

    private List<Function<Integer, Integer>> steps = new ArrayList<>();

    public void add(int i) {
        steps.add(x -> x + i);
    }

    public void subtract(int i) {
        steps.add(x -> x - i);
    }

    public void multiply(int i) {
        steps.add(x -> x * i);
    }

    public int applyTo(int i) {
        for (Function<Integer, Integer> formula : steps) {
            i = formula.apply(i);
        }
        return i;
    }
}
