package ex06;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertFalse;

public class ConfigLoaderTest {

    private String filePath = "src/ex2/data.properties";

    @Test
    @Points(2)
    public void readsExistingValue() {
        ConfigLoader loader = new ConfigLoader(filePath);

        assertThat(loader.getValue("2"), is("V2"));
        assertThat(loader.getValue("4"), is("V4"));
        assertThat(loader.getValue("6"), is("V6"));
    }

    @Test(expected = Exception1.class)
    @Points(3)
    public void throwsOnMissingValue() {
        ConfigLoader loader = new ConfigLoader(filePath);

        loader.getValue("99");
    }

    @Test(expected = Exception2.class)
    @Points(3)
    public void throwsOnUnknownFile() {
        new ConfigLoader("unknown.properties");
    }

    @Test(expected = Exception3.class)
    @Points(4)
    public void throwsOnReadError() {
        MyFileInputStream.throwOnRead = true;

        new ConfigLoader(filePath);
    }

    @Test(expected = Exception4.class)
    @Points(5)
    public void throwsOnUnknownError() {
        MyFileInputStream.throwOnRead2 = new Exception4();

        new ConfigLoader(filePath);
    }

    @After
    @Points(8)
    public void fileShouldNotBeLeftOpen() {
        assertFalse(MyFileInputStream.isOpen);
    }

    @Before
    public void reset() {
        MyFileInputStream.isOpen = false;
        MyFileInputStream.throwOnRead = false;
        MyFileInputStream.throwOnRead2 = null;
    }

}
