Assignment (25 points)

The assignment code is located in the ex26 package.

In this assignment, the tests are commented out because otherwise, they wouldn’t compile. If you write suitable types and type parameters in the Util, SumFinder, and MaxFinder classes, the tests should compile. Therefore, your primary task is to get the tests to compile.

The tests are in four files: CoalesceTests.java, SumFinderTests1.java, SumFinderTests2.java, and MaxFinderTests.java.

NB! You may modify the classes Util, SumFinder, and MaxFinder, and in these classes, you may change existing methods and class-wide type parameters. The restriction is that once the test code is uncommented, the code must compile. You may not create new classes or methods. In the tests, you must uncomment the commented-out code.

The Util class contains the method coalesce(), which takes three arguments and returns the first one that is not null. All arguments must be of the same type, and the return type must be the same as well. If none are non-null, then null is returned. This is tested in CoalesceTests.java.

In the following tasks, the classes DecNumber and HexNumber are used, both of which inherit from the superclass MyNumber. You do not need to (and must not) modify these classes—the tasks are about how to use them.

The SumFinder class has two methods for calculating sums:

a) The sum is calculated based on two arguments. The working code is commented out. If you write the correct type parameters, you can uncomment it. This part is tested in the file SumFinderTests1.java.

b) The sum is calculated by summing the elements of a list given as an argument. A comment provides a hint for solving it. This part is tested in the file SumFinderTests2.java.

The MaxFinder class can be used to calculate the maximum value from a list of elements. Write suitable type parameters and code for finding the maximum value. This part is tested in the file MaxFinderTests.java.