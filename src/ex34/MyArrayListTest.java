package ex34;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class MyArrayListTest {

    @Test
    @Points(2)
    public void canAddAndRemoveElements() {
        MyArrayList list = new MyArrayList();

        list.add(3);
        list.add(5);
        list.add(7);

        assertThat(list.removeLast()).isEqualTo(7);
        assertThat(list.removeLast()).isEqualTo(5);
        assertThat(list.removeLast()).isEqualTo(3);
    }

    @Test
    @Points(3)
    public void canGetAllElements() {
        MyArrayList list = new MyArrayList();

        list.add(5);
        list.add(1);
        list.add(9);

        assertThat(list.getElements()).containsExactly(5, 1, 9);
    }

    @Test
    @Points(5)
    public void canReverseElements() {
        MyArrayList list = new MyArrayList();

        list.add(2);
        list.add(4);
        list.add(6);
        list.add(8);

        list.reverse();

        assertThat(list.getElements()).containsExactly(8, 6, 4, 2);
    }

    @Test
    @Points(7)
    public void canRemoveFirstElement() {
        MyArrayList list = new MyArrayList();

        list.add(3);
        list.add(5);
        list.add(7);
        list.add(9);

        list.removeFirst();
        list.removeFirst();

        assertThat(list.getElements()).containsExactly(7, 9);
    }

    @Test
    @Points(8)
    public void canAddElementsToTheMiddle() {
        MyArrayList list = new MyArrayList();

        list.add(5);
        list.add(7);
        list.add(9);
        list.add(11);

        list.add(1, 10);

        assertThat(list.getElements()).containsExactly(5, 10, 7, 9, 11);
    }

    @Test
    @NoPointsIfThisTestFails
    public void doesNotUseForbiddenCode() throws IOException {

        String path = String.format("src/%s.java",
                MyArrayList.class.getName().replaceAll("\\.", "/")) ;

        String source = Files.readString(Paths.get(path));

        source = source.replaceAll("\\s", "");

        assertThat(source).doesNotContain("java.util");
        assertThat(source).doesNotContain("arraycopy");
    }
}