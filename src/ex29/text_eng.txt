Task 2 (25 points)

NB!!! At the end of the text are conditions that must be followed, otherwise the result will not be accepted.

In the package ex29, there is a class Camera and a test for testing it.

The class Camera has a method move() which allows it to be moved.
The direction and magnitude of movement are determined by the following parameters:

axes: horizontal or vertical
direction: clockwise or counterclockwise
degrees: how many degrees to move

To perform the movement, it is sufficient to modify the fields positionH (horizontal) and positionV (vertical).

The current position of the camera can be obtained using the getPosition() method. The parameter axes indicates which axis value (positionH/positionV) is being requested.

You should also add the ability to undo the last movement. For example, if the camera was last moved 3 degrees vertically clockwise, then to undo this, it should be moved 3 degrees vertically counterclockwise, so that the camera returns to the position it was in before the last movement.

The values of the fields positionH and positionV may only be changed within the move() method.

Write the necessary code so that the tests will pass.

Hints: keywords include polymorphism and anonymous functions.

NB! Conditions, the violation of which will result in no points:

To undo a movement, you must call the move() method. It is not sufficient to just change the values of positionH and positionV.

You may modify the class Camera. You may not create new classes.

You must work with the three existing fields. You may not create new fields.
The test marked with @NoPointsIfThisTestFails must pass.

The code must pass the tests from the class CameraTest.