kehvem sats tulemused
0p: 18
5p: 3
10p: 10
15p: 18
20p: 8

  Paketis ex1 on klass RotatingList.
  See peaks hoidma lisatud elemente massiivis (array), mille suurus on
  konstruktoris ette antud.

  NB!!! <PERSON><PERSON><PERSON> l<PERSON> on piirangud, mille eiramisel ülesannet ei arvestata.

  Eriliseks teeb selle listi see, et kui ruum on täis, siis hakatakse uusi elemente
  algusesse lisama olemasolevaid üle kirjutades. RotatingList-i käitumist kirjeldavad
  testid klassist RotatingListTests.

  Meetod getInternalState() tagastab sisemise massiivi seisu sõne kujul. Kui
  listis pole ühtegi elementi, siis on massiivis kõik nullid (number 0).

  Meetod toString() näitab, millised elemendid on listi lisatud. Erinevalt
  meetodist getInternalState() ei näidata tühjasid positsioone.

  Testid on järjestatud ülesande keerukuse järjekor<PERSON>. Seega on soovitatav
  kirjutada alguses vaid kood, mis esimese testi läbib jne.

  NB! Piirangud, mille eiramisel ülesannet ei arvestata.
    - Dünaamilisi kollektsioone kasutada ei tohi.

    - Muuta võite klassi RotatingList ja uusi klasse lisada ei või. RotatingList
      klassis võib olla ainult üks massiivi väli (juba olemas). Lisada võite uusi
      täisarvtüüpi välju.

    - Kood peab läbima testid klassist RotatingListTests

    - Kui @NoPointsIfThisTestFails märgisega test läbi ei lähe, siis lahendust ei arvestata.

