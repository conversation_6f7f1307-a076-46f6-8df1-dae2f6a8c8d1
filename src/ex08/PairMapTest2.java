package ex08;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class PairMapTest2 {

    @Test
    @Points(5)
    public void pairMapStoresPairValuesInSortedOrder() {

        PairMap<Integer, String> map = new PairMap<>();

        map.put(1, "a", "b");
        map.put(2, "b", "a");
        map.put(3, "c", "b");

        assertThat(map.get(1).getFirst(), is("a"));
        assertThat(map.get(1).getSecond(), is("b"));

        assertThat(map.get(2).getFirst(), is("a"));
        assertThat(map.get(2).getSecond(), is("b"));

        assertThat(map.get(3).getFirst(), is("b"));
        assertThat(map.get(3).getSecond(), is("c"));
    }
}
