Ülesanne 1 (40 punkti)

  Paketis ex1 on klass GradeStore ja test selle testimiseks.
  GradeStore peaks endas hoidma tudengeid ja nende hindeid.
  Iga tudengi kohta on eesnimi, perekonnanimi ja hinnete loetelu.
  Hinded on tähtedena: A, B, C, D, F. Hinde tähistamiseks on Enum Grade.

  Hindeid peab hoidma Enum kujul ja seda infot ei tohi duplitseerida
  nt. sõne või numbri kujul.

  NB! Teksti lõpus on veel olulisi piiranguid, mille eiramisel ülesannet ei arvestata.

  Meetod gradesFor(<eesnimi>, <perekonnanimi>) tagastab vastava tudengi hinnete
  loetelu teksti kujul. Nt. "B, A, B, C". Meetod teisendab Enum kujul hoitud hinded sõneks.

  Meetodit gradesFor() võib välja kutsuda ka nii, et mõni argument on null.
  Näiteks, kui perekonnanimi on määratmata, siis otsitakse ainult eesnime järgi. Kui sellise
  eesnimega on rohkem kui üks tudeng, siis visatakse erind MultipleMatchesException.

  Meetod getBestStudent() tagastab kõrgeima keskmise hindega tudengi nime koos tema keskmise
  hindega sõne kujul. Näiteks: "Olivia Lee 4.0".
  Keskmise hinde arvutamisel on hinded järjestatud järgmiselt: A=4, B=3, C=2, D=1, F=0.

  Meetod getBestStudent() tagastab listi kõrgeima keskmise hindega tudengitest. Iga tudengi kirje
  info on samas vomis, mis eelmises ülesandes. Näiteks: ["Olivia Lee 4.0", "David Wilson 3.75", ...].

  NB! Piirangud, mille eiramisel ülesannet ei arvestata.
    Muuta võite klasse GradeStore ja MyClass. Uusi klasse luua ei tohi.
    Klassil GradeStore võib olla ainult üks väli, mille tüübiks on List<MyClass>.
    Hinnete info hoidmiseks tuleb kasutada Enum klassi Grade ja seda infot ei tohi dublitseerida.
