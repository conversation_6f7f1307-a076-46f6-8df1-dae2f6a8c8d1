jagasin testid küll kolmeks aga sellest pole palju abi.
Kui kõik testid sisse kommenteerida, siis ei kompileeru ikka.
Peaks runnerit täiustuama. praegu käivitasin 3x ja võtsin diffi.

Ülesanne 2 (15 punkti)

  Ülesande kood asub Paketis ex2.

  NB! Selles ülesandes on testid välja kommenteeritud, kuna muidu
  need ei kompileeruks. Kui kirjutate klasside Pair ja PairMap vajalikud
  tagstustüübid ja tüübiparameetrid, peaks ka testid kompileeruma. Seega on
  teie esmane ülesanne kõik testid kompileerima saada.

  Testid on kolmes failis: PairTest.java, PairMapTest1.java ja PairMapTest2.java.

  NB! Muuta võite klasse Pair.java ja PairMap.java ning nendes klassides võite muuta kõike.
      Piiranguks on see, et kui test sisse kommenteerida, siis peab kood kompileeruma.
      Uusi klasse luua ei tohi. Testides peate väljakommenteeritud koodi sisse kommenteerima.

  Klass Pair on konteiner kahe samatüübilise elemendi hoidmiseks.

    new Pair<Integer>(1, 2) teeb uue paari, mille esimene element on 1 ja teine 2.

  Selle osa kohta on test PairTest.java.

  Klass PairMap on sõnastik (Map), mis seob võtme ja sellele vastava paari.

    PairMap<String, Integer> map = new PairMap<>();

  Loob uue sõnastiku, mille võtme tüüp on Sõne ja väärtuseks on täisarvude paarid.

    map.put("k1", 1, 2);

  Lisab sõnastikku võtme "k1" alla paari, mille esimene element on 1 ja teine 2.
  Selle osa kohta on test PairMapTest1.java.

  Sõnastik sorteerib ise paari elemendid kasvas järjekorras.
  Seega map.put("k1", 2, 1) lisab sõnastikku võtme "k1" alla paari, mille esimene
  element on 1 ja teine 2.

  Selle osa kohta on test PairMapTest2.java.