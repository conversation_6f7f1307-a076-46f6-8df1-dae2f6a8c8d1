package ex16;

import java.util.Objects;

public class DecaNumber {

    private Integer number;

    public DecaNumber(Integer number) {
        this.number = number;
    }

    @Override
    public String toString() {
        return String.valueOf(number);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof DecaNumber other)) {
            return false;
        }

        return floorToTens(this.number).equals(floorToTens(other.number));
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(floorToTens(this.number));
    }

    private Integer floorToTens(Integer number) {
        double temp = number;

        return Double.valueOf(Math.floor(temp / 10) * 10).intValue();
    }
}
