package ex17;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;

public class MyLargeNumberTests {

    @Test
    @Points(2)
    public void canHandleSingleDigitNumbers() {
        MyLargeNumber n1 = new MyLargeNumber("1");
        MyLargeNumber n2 = new MyLargeNumber("2");

        assertThat(n1.add(n2).toString(), is("3"));
    }

    @Test
    @Points(3)
    public void canHandleMultiDigitNumbers() {
        MyLargeNumber n1 = new MyLargeNumber("123");
        MyLargeNumber n2 = new MyLargeNumber("222");

        assertThat(n1.add(n2).toString(), is("345"));
    }

    @Test
    public void forDevelopment() {
        // different length
        // carry

        MyLargeNumber n1 = new MyLargeNumber("12");
        MyLargeNumber n2 = new MyLargeNumber("3");

        System.out.println(n1.add(n2));
    }

    @Test
    @Points(10)
    public void largeNumbersSameLengthNoCarry() {
        MyLargeNumber n1 = new MyLargeNumber("14231423142314231423142314231423");
        MyLargeNumber n2 = new MyLargeNumber("41303141303141303141303141301130");

        assertThat(n1.add(n2).toString(),
                is("55534564445455534564445455532553"));
    }

    @Test
    @Points(12)
    public void largeNumbersDifferentLengthNoCarry() {
        MyLargeNumber n1 = new MyLargeNumber("14231423142314231423142314231423");
        MyLargeNumber n2 = new MyLargeNumber("4001303141303241303141303142301100");

        assertThat(n1.add(n2).toString(),
                is("4015534564445555534564445456532523"));
    }

    @Test
    @Points(10)
    public void largeNumbersDifferentLengthWithCarry() {
        MyLargeNumber n1 = new MyLargeNumber("142345028205805725892314298731423");
        MyLargeNumber n2 = new MyLargeNumber("8579342578928028501080688120860");

        assertThat(n1.add(n2).toString(),
                is("150924370784733754393394986852283"));
    }

    @Test
    @Points(3)
    public void supportsPowerOperations() {
        assertThat(new MyLargeNumber("7").power(42).toString(),
                is("311973482284542371301330321821976049"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {

        String path = "src/ex17/" + MyLargeNumber.class.getSimpleName() + ".java";

        String source = Files.readString(Paths.get(path));

        assertThat(source, not(containsString("util.Math")));
        assertThat(source, not(containsString("BigInteger")));
        assertThat(source, not(containsString("BigDecimal")));
    }

}