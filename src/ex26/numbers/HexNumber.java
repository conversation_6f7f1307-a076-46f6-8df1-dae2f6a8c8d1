package ex26.numbers;

public class Hex<PERSON>umber extends MyNumber {

    private Integer value;

    public HexNumber(int value) {
        this.value = value;
    }

    @Override
    public MyNumber add(MyNumber addend) {
        return new HexNumber(value + addend.intValue());
    }

    @Override
    public Integer intValue() {
        return value;
    }

    @Override
    public String toString() {
        return Integer.toHexString(value).toUpperCase();
    }

    @Override
    public int compareTo(MyNumber o) {
        return this.value.compareTo(o.intValue());
    }
}
