package ex15;

import java.util.LinkedList;

public class Robot {

    private int positionX = 0;
    private int positionY = 0;

    private LinkedList<Runnable> undoOperations = new LinkedList<>();

    public void moveNorth(int distance) {
        System.out.printf("moving %d clicks North\n", distance);

        positionX += distance;

        undoOperations.add(() -> moveSouth(distance));
    }

    public void moveSouth(int distance) {
        System.out.printf("moving %d clicks South\n", distance);

        positionX -= distance;

        undoOperations.add(() -> moveNorth(distance));
    }

    public void moveWest(int distance) {
        System.out.printf("moving %d clicks West\n", distance);

        positionY -= distance;

        undoOperations.add(() -> moveEast(distance));
    }

    public void moveEast(int distance) {
        System.out.printf("moving %d clicks East\n", distance);

        positionY += distance;

        undoOperations.add(() -> moveWest(distance));
    }

    public int getPositionX() {
        return positionX;
    }

    public int getPositionY() {
        return positionY;
    }

    public void retractLast() {
        undoOperations.removeLast().run();

        undoOperations.removeLast();
    }
}
