package ex34;

public class MyArrayList {
    private int[] elements;
    private int size;
    private static final int DEFAULT_CAPACITY = 10;

    public MyArrayList() {
        elements = new int[DEFAULT_CAPACITY];
        size = 0;
    }

    public void add(int element) {
        elements[size++] = element;
    }

    public int[] getElements() {
        int[] result = new int[size];
        arrayCopy(elements, 0, result, 0, size);
        return result;
    }

    public int removeLast() {
        return elements[--size];
    }

    public void reverse() {
        for (int i = 0; i < size / 2; i++) {
            int temp = elements[i];
            elements[i] = elements[size - 1 - i];
            elements[size - 1 - i] = temp;
        }
    }

    public void removeFirst() {
        if (size > 0) {
            arrayCopy(elements, 1, elements, 0, size - 1);
            size--;
        }
    }

    public void add(int position, int element) {
        if (position < 0 || position > size) {
            throw new IndexOutOfBoundsException();
        }

        for (int i = size; i > position; i--) {
            elements[i] = elements[i - 1];
        }

        elements[position] = element;
        size++;
    }

    public static void arrayCopy(int[] src, int srcPos, int[] dest, int destPos, int length) {
        for (int i = 0; i < length; i++) {
            dest[destPos + i] = src[srcPos + i];
        }
    }
}
