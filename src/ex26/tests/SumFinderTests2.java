package ex26.tests;

import ex26.SumFinder;
import ex26.numbers.DecNumber;
import ex26.numbers.HexNumber;
import ex26.numbers.MyNumber;
import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class SumFinderTests2 {

    @Test
    @Points(7)
    public void canSumTwoHexNumbers() {

        SumFinder sumFinder = new SumFinder();

        List<HexNumber> hexNumbers = List.of(
                new HexNumber(4),
                new HexNumber(2),
                new HexNumber(1));

        MyNumber hexSum = sumFinder.sum(hexNumbers);

        assertThat(hexSum.intValue(), is(7));

        List<DecNumber> decNumbers = List.of(
                new DecNumber(7),
                new DecNumber(2),
                new DecNumber(3));

        MyNumber decSum = sumFinder.sum(decNumbers);

        assertThat(decSum.intValue(), is(12));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotAddNewMethods() {
        long methodCount = Arrays.stream(SumFinder.class.getDeclaredMethods()).count();

        assertThat(methodCount, is(2L));
    }
}
