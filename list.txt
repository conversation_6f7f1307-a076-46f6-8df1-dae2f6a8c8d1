
ex1 LightController - polymorphism
ex2 Exceptions - exceptions (kehv)
ex3 Odometer - algo
ex4 ScoreCounter - entry
ex5 NumberStore - array, manual sort
ex6 ConfigLoader - exceptions
ex7 Board - algo (isIsolated)
ex8 PairMap - generics, collections
ex9 UniqueNameFinder - collections
ex10 BatchStore - generics, polymorphism
ex11 NumberGroup - entry
ex12 FormulaComposer - entry, fp
ex13 FormulaGenerator - polymorphism
ex14 GradeRepository - entry, sort
ex15 Robot - undo, fp, polymorphism
ex16 DecaNumber - toString, equals, hashCode
ex17 MyLargeNumber - collections, algo
ex18 RotatingList - array, algo
ex19 BatchIterator - collections, algo
ex20 ArrayDeque - array, algo
ex21 WordSearch (ab**) - algo
ex22 GradeStore - entry, enum, sort
ex23 ArrayMap - array, algo
ex24 DimensionStore - array, algo
ex25 ResultsStore - generics, collections (ex8 PairMap analog)
ex26 DecNumber and HexNumber. MaxFinder.java, SumFinder.java, Util.coalesce() - generics
ex27 ScientificNumber, SimpleNumber - polymorphism, parse text
ex28 WordStore - sorting by external property
ex29 Camera - undo, fp, polymorphism (ex15 analog)
ex30 DiffCalculator - algo
ex31 Board (chunk size of true neighbours) - algo
ex32 ArrayStoreCounter (store number in byte array) - algo
ex33 Combinations (väga sarnane kahendsüsteemi teisendusele. sobiks nt. viimaseks eksamiks).
ex34 MyArrayList - algo (reverse, removeFirst, add to pos)
ex35 StreamAPI (5 different exercises)
ex36 Contestant (equals() hashCode())
ex37 VersionedDocument (undo/command, fp, polymorphism)
ex38 CompactMatrix (algo, array, PointSet)



