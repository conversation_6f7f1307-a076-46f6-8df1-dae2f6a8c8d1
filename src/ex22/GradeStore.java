package ex22;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class GradeStore {

    private List<MyClass> students = new ArrayList<>();

    public GradeStore() {
        for (String line : loadFile()) {
            String[] nameParts = line.split(":")[0].split(" ");

            String firstName = nameParts[0];
            String lastName = nameParts[1];

            List<Grade> grades = Arrays.stream(line.split(": ")[1].split(", "))
                    .map(Grade::valueOf).toList();

            students.add(new MyClass(firstName, lastName, grades));
        }
    }

    public List<String> loadFile() {
        try {
            return Files.readAllLines(Paths.get("src/ex22/grades.txt"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public String getBestStudent() {
        return getBest5Students().get(0);
    }

    public List<String> getBest5Students() {
        return students.stream()
                .sorted((a, b) -> Double.compare(getGPA(b.grades), getGPA(a.grades)))
                .limit(5)
                .map(p -> p.firstName + " " + p.lastName + " " + getGPA(p.grades))
                .toList();
    }

    private double getGPA(List<Grade> grades) {
        return grades.stream()
                .mapToDouble(g -> 4 - g.ordinal())
                .average()
                .orElse(0);
    }

    public String gradesFor(String firstName, String lastName) {
        List<MyClass> found = students.stream()
                .filter(p -> firstName == null || p.firstName.equals(firstName))
                .filter(p -> lastName == null || p.lastName.equals(lastName))
                .toList();

        if (found.size() > 1) {
            throw new MultipleMatchesException();
        }

        return found.get(0).grades.stream()
                .map(Grade::toString)
                .collect(Collectors.joining(", "));
    }
}
