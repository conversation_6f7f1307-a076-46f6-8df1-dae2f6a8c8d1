package ex26.tests;

import ex26.MaxFinder;
import ex26.numbers.DecNumber;
import ex26.numbers.HexNumber;
import ex26.numbers.MyNumber;
import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class MaxFinderTests {

    @Test
    @Points(9)
    public void findsMaximumOfDifferentKindOfMyNumbers() {

        MaxFinder<MyNumber> maxFinder = new MaxFinder<>();

        List<HexNumber> hexNumberList = List.of(
                new HexNumber(1),
                new HexNumber(110),
                new HexNumber(4));

        MyNumber maxHex = maxFinder.max(hexNumberList);

        assertThat(maxHex.intValue(), is(110));
        assertThat(maxHex.toString(), is("6E"));

        List<MyNumber> decNumberList2 = List.of(
                new DecNumber(11),
                new DecNumber(10),
                new DecNumber(4));

        MyNumber maxDec = maxFinder.max(decNumberList2);

        assertThat(maxDec.intValue(), is(11));
        assertThat(maxDec.toString(), is("11"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotAddNewMethods() {
        long methodCount = Arrays.stream(MaxFinder.class.getDeclaredMethods()).count();

        assertThat(methodCount, is(1L));
    }


}
