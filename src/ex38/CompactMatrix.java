package ex38;

public class CompactMatrix {
    
    private static final int CAPACITY = 10; // enough for all the test cases

    private final int[] rowIndices = new int[CAPACITY];
    private final int[] colIndices = new int[CAPACITY];
    private final int[] values = new int[CAPACITY];

    private int elementCount = 0;

    public void add(int row, int col, int value) {
        Integer index = findElement(row, col);

        if (index == null) {
            index = elementCount;
            elementCount++;
        }

        rowIndices[index] = row;
        colIndices[index] = col;
        values[index] = value;
    }
    
    public Integer get(int row, int col) {
        Integer index = findElement(row, col);
        return index == null ? null : values[index];
    }
    
    public int getElementCount() {
        return elementCount;
    }
    
    public int getRowSum(int row) {
        int sum = 0;
        for (int i = 0; i < elementCount; i++) {
            if (rowIndices[i] == row) {
                sum += values[i];
            }
        }
        return sum;
    }
    
    public Integer[][] toOrdinaryMatrix() {
        int maxRowIndex = -1;
        int maxColIndex = -1;
        for (int i = 0; i < elementCount; i++) {
            if (rowIndices[i] > maxRowIndex) {
                maxRowIndex = rowIndices[i];
            }
            if (colIndices[i] > maxColIndex) {
                maxColIndex = colIndices[i];
            }
        }

        Integer[][] result = new Integer[maxRowIndex + 1][maxColIndex + 1];
        
        for (int i = 0; i < elementCount; i++) {
            result[rowIndices[i]][colIndices[i]] = values[i];
        }
        
        return result;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("[");

        for (int i = 0; i < elementCount; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(String.format("(%d;%d)=%d",
                    rowIndices[i], colIndices[i], values[i]));
        }

        sb.append("]");

        return sb.toString();
    }
    
    private Integer findElement(int row, int col) {
        for (int i = 0; i < elementCount; i++) {
            if (rowIndices[i] == row && colIndices[i] == col) {
                return i;
            }
        }
        return null;
    }
}
