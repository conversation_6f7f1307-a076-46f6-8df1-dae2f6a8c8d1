Propose an exercise for Java course exam. It should test the students’
knowledge of Java generics. One sample exercise from previous jear
is @ex26/text_eng.txt. This package contains the solved code as well.
Students have access to exercises from previous years so new exercise
should be different enough that the solution can’t be just copied.

I like the idea of a messaging system but the concrete example is too complicated. It is introductory course and students have only on hour to complete the solution.

The exercise should be structured so that weak students get some points if they have even minimal understanding of the material. coalesce() in the example required very little, SumFinder more and Max<PERSON><PERSON> even more.

The ideas that should be tested:
- simple type parameters for class or method
- type parameter bounds
- wildcards

Do not provide both solution and initial state. Solution is enough.


