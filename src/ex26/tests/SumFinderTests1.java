package ex26.tests;

import ex26.SumFinder;
import ex26.numbers.DecNumber;
import ex26.numbers.HexNumber;
import ex26.numbers.MyNumber;
import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.util.Arrays;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class SumFinderTests1 {

    @Test
    @Points(3)
    public void findsSumOfDifferentKindOfMyNumbers() {

        SumFinder sumFinder = new SumFinder();

        MyNumber hexSum = sumFinder.sum(new HexNumber(4), new HexNumber(2));

        assertThat(hexSum.intValue(), is(6));

        MyNumber decSum = sumFinder.sum(new DecNumber(3), new HexNumber(6));

        assertThat(decSum.intValue(), is(9));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotAddNewMethods() {
        long methodCount = Arrays.stream(SumFinder.class.getDeclaredMethods()).count();

        assertThat(methodCount, is(2L));
    }

}
