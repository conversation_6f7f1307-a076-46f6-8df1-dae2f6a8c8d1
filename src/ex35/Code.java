package ex35;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class Code {

    public static int sum(List<Integer> integers) {
        return integers.stream().mapToInt(i -> i).sum();
    }

    // public static Optional<?> findFirst(List<?> list, Predicate<?> criteria)
    public static <T> Optional<T> findFirst(List<T> list, Predicate<T> criteria) {
        // Optional.of(obj); // creates Optional that contains the object
        // Optional.empty(); // creates empty Optional

        for (T each : list) {
            if (criteria.test(each)) {
                return Optional.of(each);
            }
        }

        return Optional.empty();
    }

    public static boolean retry(Runnable operation, int totalAttempts) {
        for (int attempt = 0; attempt < totalAttempts; attempt++) {
            try {
                operation.run();

                return true;
            } catch (Exception ignored) {}
        }

        return false;
    }

    public static List<Integer> flatten(List<List<Integer>> lists) {
        // ArrayList.addAll(otherList); adds all elements from otherList.

        return lists.stream().reduce((a, b) -> {
            List<Integer> result = new ArrayList<>(a);
            result.addAll(b);
            return result;
        }).get();
    }

    public List<String> sortByCFrequency(List<String> words) {
        return words.stream()
                .map(word -> new Structure(word, getFrequency(word, 'c')))
                .sorted(Comparator.comparingInt(Structure::b).thenComparing(Structure::a))
                .map(Structure::a)
                .toList();

    }

    public static Map<Character, List<String>> toMap(List<String> words) {
        // ArrayList.addAll(otherList); adds all elements from otherList.

        return words.stream().collect(Collectors.toMap(
                each -> each.charAt(0),
                List::of,
                (a, b) -> {
                    var result = new ArrayList<>(a);
                    result.addAll(b);
                    return result;
                }
        ));
    }


    int callCount = 0;

    private int getFrequency(String word, char c) {
        callCount++;
        List<String> letters = Arrays.stream(word.split("")).toList();
        return Collections.frequency(letters, String.valueOf(c));
    }
}
