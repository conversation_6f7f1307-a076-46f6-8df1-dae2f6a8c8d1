Ülesanne 2 (25 punkti)

  NB!!! <PERSON><PERSON><PERSON> lõpus on tingimiused, mille eiramisel ei lähe tulemus arvesse.

  Paketis ex2 on klass Camera ja test selle testimiseks.

  Klassil Camera on meetod move() mis võimaldab selle liigutamist.
  Liikumise suuna ja suuruse määravad järgmised parameetrid:
    axes: horisontaalne või vertikaalne
    direction: päripäeva või vastupäeva
    degrees: mitu kraadi liikuda

  Liikumise teostamiseks piisab väljade positionH (horisontaalne) ja positionV
  (vertikaalne) muutmisest.

  Kaamera hetke asukoha saab küsida getPosition() meetodi abil. Parameeter axes
  ütleb, millise telje väärtuse (positionH/positionV) soovime saada.

  Lisaks peaksite lisama võimaluse viimase liigutamise tagasivõtmiseks. Kui kaamerat
  liigutati viimati 3 kraadi vertikaalselt päripäeva, siis selle tagasivõtmiseks peaks
  seda liigutama 3 kraadi vertika<PERSON>elt vastupäeva, et kaamera
  oleks uuesti selles asendis, milles ta oli enne viimast liikumist.

  Väljade positionH ja positionV väärtusi tohib muuta ainult move() meetodis.

  Kirjutage vajalik kood, et testid tööle hakkaksid.

  Vihjeks on märksõnad polümorfism ja anonüümsed funktsioonid.

  NB! Tingimiused, mille eiramisel ei lähe tulemus arvesse.
    - Liikumise tagasivõtmiseks peate move() meetodit väja kutsuma, ei piisa
      lihtsalt positionH ja positionV väärtuste muutmisest.
    - Muuta võite klassi Camera. Uusi klasse luua ei või.
    - Peate hakkama saama kolme olemasoleva väljaga. Uusi välju luua ei või.
      @NoPointsIfThisTestFails märgisega test peab õnnestuma.
    - Kood peab läbima testid klassist CameraTest.
