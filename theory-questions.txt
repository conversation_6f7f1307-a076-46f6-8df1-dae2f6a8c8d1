
### Types ########################################################################

    Ujuvko<PERSON> arvud, ei sobi finantsarvutuste tegemiseks. Miks?

    Allolev koodirida väljastab tulemuse "false". Miks?
      System.out.println(0.7 == 7 * 0.1);

    Float.MAX_VALUE == Float.MAX_VALUE - 1. Miks?

    Millistel juhtudel on Javas mõistlik kasutada masiivi listi asemel?
    chatGPT: Arrays are reasonable when the size is fixed and known in advance, direct element access is required, or low memory overhead is desired.

    Millal on mõtet kasutada numbrite hoidmiseks primitiive (nt. int)
    ja millal objekte (nt. Integer)?

    Mis probleem? (new)
    public static Integer minimumElement(int[] integers) {
        Integer minimumElement = integers[0]; // ei sobi!

    Allolev kood väljasab väärtuse false. Miks?
    Integer x = 200; Integer y = 200; System.out.println(x == y);


### testing ###################################################################

   Üksustestid (unit test) aitavad leida vigu. Nimetage veel midagi olulist,
   mis nendest kasu saab.

   Üksusteste (unit test) võib vaadata, kui koodi dokumentatsiooni. Seletage,
   mida selle all mõeldud on?

   Mis määrab selle, kas allolev kontroll õnnestub või mitte?
   assertThat(new Person()).isEqualTo(new Person());

### OO ########################################################################

    2025 sessioonile jäi see jutt ära
    Mis asi on objekti vaikimisi (default) konstruktor?
    chatGPT: The default constructor is a special constructor automatically generated by Java if no constructor is explicitly defined in a class. It initializes the object with default values and takes no arguments.

    Objekt aitab varjata teostuse detaile. Miks peaks midagi varjama?

    Mis kasu on sellest, et objekt aitab detaile varjata?
    (chatGPT: Abstraction and encapsulation.)

    Mis kasu on Javas klassidest (või objektidest)?

    Javas võib kokkukuuluvaid andmeid hoida näiteks massiivis aga
    parem viis oleks luua eraldi klass nende hoidmiseks.
    Nimetage kaks põhjust, miks viimane parem lahendus on?

    Mis kasu on Javas objekti konstruktorist?

    (new)
    Kas alloleva meetodi väljakutsuja jaoks on pärast väljakutset kaasa
    antud parameetri väärtus muutunud? 1p. Miks? 4p.

    public void someMethod(Person person) { person.setName("Bob"); }

### Inheritance ###################################################################

   1. Mis kasu on Javas liidestest (interface)?

   1. Mis on Javas liidese ja abstraktse klassi vahe?

   2. Pärimise hierarhia loomisel on väga soovitatav jälgida klasside
      vahelist "is a" suhet. Miks?

  3. Mis on abstraktses klassis abstraktse meetodi mõte?

   Mida tähendab "protected" juudepääsu modifikaator (access modifier)?

   Abstraktses klassis puudub mõnedel meetoditel implementatsioon.
   Miks selliseid ilma implementatsioonita meetodeid sinna vaja on?

### Enum ###################################################################

   Mis kasu on Enum tüüpidest?

   Miks eelistada Enum tüüpi konstante sõne tüüpi konstantidele?

### Cast ###################################################################

  Mis probleem võib tekkida objektide cast-imisel?

  Mis probleem võib tekkida primitiivide cast-imisel? (validated by ChatGPT)

  1. Kui allolev kood kompileerub, siis MIS VIGA saab juhtuda real 2.

     1. long x = ...;
     2. long y = (int) x;

  1. Kui allolev kood kompileerub, siis MIS VIGA saab juhtuda real 2.

     1. long x = ...;
     2. float y = x;

    Cast operatsioon (nt. var p = (Person) object;) tähendab primitiivide ja
    objektide puhul erinevat asja. Milles on erinevus?

### Import #########################################################################

    Mida teeb allolev rida?

      import static org.hamcrest.CoreMatchers.*;

    Mida tähendab allolevas koodis "static"?

      import static org.hamcrest.CoreMatchers.is;

### Exceptions ###################################################################

    Mis on erindite tõlkimise eesmärk?

    Mis on alloleva koodi mõte?

    try {
        // read file
    } catch (IOException e) {
        throw new RuntimeException(e);
    }

    Mis on alloleva koodi mõte?

    try {
        // read translation file
    } catch (IOException e) {
        throw new MissingLanguageFileException(e);
    }

    Mis on Javas erindite mähkimise (wrapping) eesmärk?
    Siia alla läheb ka tõlkimine ja muu selline. Täpsustada Java spetsiifiline. või runtimeException

    Erindit võib vaadata, kui lisa infokanalit (lisaks tagastusväärtusele).
    Mis kasu sellisest lisa infokanalist on?

    Kontrollitud erindite (checked exception) kasutamine toob endaga kaasa lisatööd?
    Millist?

    Mis kasu on Javas erinditest (exception)?

    Mis vahe on kontrollitud (checked) ja mitte kontrollitud (unchecked) erinditel (exception)?

  4. Mis on alloleva koodi MÕTE?

     try {
         ...
     } catch (IOException e) {
         throw new RuntimeException(e);
     }


   4. Alloleva koodi disainiga on midagi väga valesti. Mis? (chatgpt validated)

        int x = ...;
        int y = ...;

        try {
            x /= y;
        } catch (ArithmeticException e) {
            x = 0;
        }

   4. Allolev kood on väga halva disainiga. Kuidas seda parandada?

        try {
            return numbers.get(i);
        } catch (IndexOutOfBoundsException e) {
            return 0;
        }

  2. try/catch ploki abil saame vea korral programmi tööga edasi minna.
     Mõnede vigade puhul on try/catch ploki kasutamine sobilik ja teiste
     puhul mitte. Mille põhjal seda otsustada?

    Meil on erindid E1 ja E2:
      public class E1 extends RuntimeException {}
      public class E2 extends Exception {}
    Kui minu kood kasutab siiani klassi E1, siis kas saan selle lihtsalt
    (find & replace) E2 vastu vahetada? Miks?

      # räägiti palju, et RuntimeException on Exception-i alamklass.

### Generics ###################################################################

    Mida tähendab alloleval real "? extends Number" konstruktsioon?
      List<? extends Number> numbers;
    Mille poolest erineb see tüübist "Number"?

    used on test0
    Mis on Javas geneeriliste tüüpide tüübi piirangute (bounded type parameters) eesmärk?
    chatGPT: The purpose of bounded type parameters in Java generics is to restrict the types that can be used as arguments. By specifying upper or lower bounds, it ensures that only specific types or their subclasses/superclasses are accepted, allowing for more precise type constraints and enabling enhanced code safety and flexibility.

   3. Mis kasu on tüübi parameetritest (nt MyClass<MyTypeParameter>)?

    Allolev kood ei kompileeru. Miks? Kuidas seda parandada?

    public <T> T min(T a, T b) {
        return a.compareTo(b) < 0 ? a : b;
    }

    Kas alloleva meetodi väljakutse validate("Alice", 20) kompileerub? 1p Miks? 4p.

    public <T> boolean validate(T a, T b) { ...


    Allolev kood ei kompileeru. Kuidas seda parandada?
    Piisab põhimõtte kirjeldusest, täpset koodi pole vaja.

    public <T> boolean myContains(T list, Integer element) {
        return list.contains(element);
    }
    vastus: Integer element peaks olema T element


    class {
        public List<* extends Number> min() {

        }
    }
    Mida kirjutada tärini asemele? 1p Mida see tähendab? 4p.

### Collections #########################################################################

  Hulgas on elemendid ühekordselt aga allolev kood väljastab ühe asemel kaks.
  Kuidas seda parandada?

      Set<Point> set = new HashSet<>();
      set.add(new Point(1, 1));
      set.add(new Point(1, 1));
      System.out.println(set.size());

  used on test0
  Kui kirjutada üle klassi equals() meetod, siis peaks üle kirjutama ka hashCode() meetodi.
  Miks?

  Allolev kood peaks sisendlistist eemaldama korduvad elemendid.
  Tulemuses on aga elementide järjekord muutunud.
  Miks? Kuidas seda parandada?

    List<Integer> input = List.of(5, 2, 3, 2);
    List<Integer> result = new ArrayList<>(new HashSet<>(input));
    System.out.println(result); // prints: [2, 3, 5]

  Milline oluline käitumise erinevus on klassidel HashSet ja LinkedHashSet?

  2. Meil on vaja hoida infot erakondade ja nende valimistulemuste kohta.
       REF => 28,9
       KESK => 23,1
       EKRE => 17,8

     Selleks sobiks sõnastik aga see oleks lühinägelik lahendus. Miks?

### FP #########################################################################

    used on test0
    Funktsionaalse programmeerimise põhimõtete kohaselt on olek (state) halb. Miks nii?
    ChatGPT: Funktsionaalses programmeerimises peetakse olekut halvaks, kuna see võib tekitada
    keerukust ja soodustada ootamatuid kõrvalmõjusid. Funktsionaalses programmeerimises rõhutatakse puhtaid, mitte-muudetavaid funktsioone, mis tagavad deterministliku käitumise.

    Mis kasu on java.util.Optional klassist? Konkreetselt palun. Ei sobi loetavus vms.

    Mida tähendab allolevas koodis System.out::println? Ehk, mis saab olema muutujas c.
    Consumer<String> c = System.out::println;

### Poly ###################################################################

    Nimetage mingi konkreetne kasu polümorfismist koodi disains?
    Ei sobi liialt üldised vastused nagu "loetavam", "lühem" või "paindlikum".
    chatGPT: See võimaldab koodi kergesti kohandada ja laiendada uutele nõuetele, ilma olemasolevat koodi muutmata.

    Mis kasu on Javas polümorfismist?

    Meil on meetodi deklaratsioon: public String getName(Person p) { ...
    Kas allolev väljakutse vastab sellele? 1p. Miks? 4p.

    Object alice = new Person("Alice");
    getName(alice);   Note: väga paljud vastasid õigesti aga tundub, et kogemata. (ürtatakse sisestada Objectit mitte Personit.)


### Annotations #########################################################################

   Mis kasu on Javas annotatsioonidest?

    Tooge näide alloleva MyAnnotation annotatsiooni kasutamisest.
    public @interface MyAnnotation {
        Class<? extends Throwable> exception();
    }

    ... vastus tuleb siia ...
    public void myMethod()
    ------------

    Mis kasu on @Override annotatsioonist?


### Reflection #########################################################################

    Mis kasu on Javas refleksioonist?
    ChatGPT: Java reflection allows dynamic exploration and manipulation of a program's structure, behavior, and state, providing flexibility and enabling advanced programming techniques.

    Tooge kaks näidet, milleks Java refleksiooni kasutada.

### Concurrent #########################################################################

   Kui protsessoril on 8 tuuma, siis millisel juhul on mõtet panna käima
   100 (sõltumatut) samaaegset tööd.

   Ühetuumaline protsessor saab käivitada korraga 10 lõime. Kuidas on võimalik ühel
   tuumal mitut programmi korraga käivitada?

   Mida tähendab allolevas koodis "synchronized"
   private synchronized void swap2 { ...

   (new)
   Meil on muutuja x = 0 mida samaaegselt muudavad kaks lõime. Üks suurendab seda
   1000 korda (x++) ja teine vähendab (x--) 1000 korda. Tulemus ole 0. Vastake võimalikult
   täpselt miks nii?

### Other #########################################################################

   Mis probleem on globaalsete muutujatega?

   Mis on Java klassi "static" meetodid ja väljad?

    Millisel juhul võib allolevas koodis rida 2 veaga lõppeda?

      1. Boolean x = isCellMarked();
      2. if (x) {
      3.     System.out.println("x is true");
      4. }


    Mida peaksin tegema, et allolev koodirida kompileeruks?
    Meetod of() peaks olema viide java.util.List liideses olevale of() meetodile.
    Seda rida ennast muuta ei või ja uut sellise nimega meetodit ei soovi teha.

      List<Integer> numbers = of(1, 2, 3);

Mis on koodi Java koodi kompileerimise käigus tehtava staatilise
analüüsi eesmärk?

Mida näitab stack trace?

Meetodite ülelaadimine (overload) lubab mitut sama nimega meetodit?
Mis kasu sellest on?

Mis on meetodite ülelaadimine (overload)?

Java paketid (package) toimivad nimeruumina. Mis kasu sellest on?
chatGPT: The namespace provided by Java packages helps prevent naming conflicts, facilitates organization, and enhances code modularity. It allows developers to create unique identifiers for classes, interfaces, and other elements, ensuring clarity, reusability, and maintainability of code within larger projects.

Mida sisaldab Java jar laiendiga fail?

Millistel juhtudel allolev kood vea annab?

private static int countDollarSymbols(String[] symbols) {
    int count = 0;

    for (String symbol : symbols) {
        if (symbol.equals("$")) {
            count++;
        }
    }

    return count;
}
