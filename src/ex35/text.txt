Ülesanne 1 (25 punkti)

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Paketis ex1 on klass Code ja testid selle käitumise kontrollimiseks.
  Klassis Code on viis meetodit, mille sisu peate kirjutama.

  sum() - Leiab täisarvude listi summa.

  findFirst() - <PERSON><PERSON>b esimese elemendi, mis vastab etteantud tingimusele.
                Siin peate lahenduse kirjutama ilma Stream API võimalusi kasutamata (for, if)
                Siin võite muuta ka meetodi parameetrite tüüpe, nii et testid kompileeruks.

  retry() - Võtab sisse koodi ja käivitab selle. Kui kood viskab vea, siis proovitakse
            uuesti. Kokku proovitakse käivitamist "totalAttempts" korda.
            Meetod tagastab true kui kood õnnestus käivitada ilma, et see viskaks vea.

           TODO: pole vaja stream api-t

  sortByCFrequency() - Sorteerib etteantud sõned c-tähe esinemise sageduse järgi.
                       kui kahes sõnes esineb seda sama palju, siis sorteeritakse tähestiku järjekorras.
                       c-tähe esinemise leidmiseks peate kasutama Code.getFrequency() meetodit
                       ja seda tohite iga sõna kohta välja kutsuda vaid ühe korra.

  toMap() - Tagastab sõnastiku, mille võtmeteks on etteantud sõnede esimene täht ja väärtuseks
            list sõnadest, mis selle tähega algavad.

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi Code ja uusi faile lisada ei või.
      Olemasolevaid klasse võite kasutada, milleks soovite.
    - Peate kasutama Stream API võimalusi ja tavalisi tsükleid (for, while, ...)
      või tingimuslauseid (if, ...) kasutada ei või.
      Erandiks on findFirst(), milles on vastupidine nõue.

