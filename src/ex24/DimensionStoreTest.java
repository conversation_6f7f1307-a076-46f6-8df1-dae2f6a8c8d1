package ex24;

import org.hamcrest.Matcher;
import org.hamcrest.MatcherAssert;
import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.empty;
import static org.junit.Assert.assertThat;

public class DimensionStoreTest {

    @Test
    @Points(5)
    public void canStoreIntValues() {
        DimensionStore store = new DimensionStore();

        store.add(2);
        store.add(3);

        assertThat(store.size(), is(2));
        assertThat(store.getLast(), is(3));
    }

    @Test
    @Points(4)
    public void returnsArrayOfAddedValues() {
        DimensionStore store = new DimensionStore();

        store.add(2);
        store.add(3);
        store.add(4);

        assertThat(store.values(), is(arrayContaining(2, 3, 4)));
    }

    @Test
    @Points(4)
    public void canRemoveFirstValue() {
        DimensionStore store = new DimensionStore();

        store.add(2);
        store.add(3);
        store.add(4);

        assertThat(store.shift(), is(2));
        assertThat(store.values(), is(arrayContaining(3, 4)));
    }

    @Test
    @Points(5)
    public void canHandle12elements() {
        commonDimensionStoreTests(12);
    }

    @Test
    @Points(7)
    public void canHandle24elements() {
        commonDimensionStoreTests(24);
    }

    @Test
    @NoPointsIfThisTestFails
    public void constantsAreNotChanged() {
        assertThat(DimensionStore.X_DIMENSION, is(4));
        assertThat(DimensionStore.Y_DIMENSION, is(3));
        assertThat(DimensionStore.Z_DIMENSION, is(2));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldHaveOnlyAllowedFields() {
        List<Field> fieldsNotAllowed = Arrays.stream(DimensionStore.class.getDeclaredFields())
                .filter(field -> !field.getType().equals(int.class))
                .filter(field -> !field.getType().equals(int[].class))
                .filter(field -> !field.getType().equals(int[][].class))
                .filter(field -> !field.getType().equals(int[][][].class))
                .toList();

        MatcherAssert.assertThat(fieldsNotAllowed, is(empty()));
    }


    public void commonDimensionStoreTests(int testDataSize) {
        DimensionStore store = new DimensionStore();

        int[] testData = getTestValues(testDataSize);

        store.addAll(testData);

        assertThat(store.size(), is(testDataSize));
        assertThat(store.values(), is(arrayContaining(testData)));
        assertThat(store.shift(), is(testData[0]));
        assertThat(store.shift(), is(testData[1]));
        assertThat(store.size(), is(testDataSize - 2));

        // remove 2 elements from test data;
        testData = shift(shift(testData));

        assertThat(store.values(), is(testData));

    }

    private int[] getTestValues(int count) {
        return IntStream.rangeClosed(1, count).toArray();
    }

    private int[] shift(int[] values) {
        return Arrays.stream(values).skip(1).toArray();
    }

    public static Matcher<int[]> arrayContaining(int... items) {
        return is(items);
    }

}
