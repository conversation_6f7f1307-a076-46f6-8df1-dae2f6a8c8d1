package ex31;

public class Board {

    private boolean[][] board;

    public Board(int rows, int cols) {
        board = new boolean[rows][cols];
    }

    public int getChunkSize(int row, int col) {
        if (!isMarked(row, col)) {
            return 0;
        }

        return Math.max(getVerticalChunkSize(row, col),
                getHorizontalChunkSize(row, col));
    }

    public int getHorizontalChunkSize(int row, int col) {
        int count = 0;
        col = findStartCol(row, col);

        while (isMarked(row, col)) {
            count++;
            col++;
        }

        return count;
    }

    public int getVerticalChunkSize(int row, int col) {
        int count = 0;
        row = findStartRow(row, col);

        while (isMarked(row, col)) {
            count++;
            row++;
        }

        return count;
    }

    private int findStartCol(int row, int col) {
        while (isMarked(row, col - 1)) {
            col--;
        }
        return col;
    }

    private int findStartRow(int row, int col) {
        while (isMarked(row - 1, col)) {
            row--;
        }
        return row;
    }

    private boolean isMarked(int row, int col) {
        if (row < 0 || row >= board.length
                || col < 0 || col >= board[0].length) {
            return false;
        }

        return board[row][col];
    }

    public void mark(int row, int col) {
        board[row][col] = true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (boolean[] row : board) {
            for (boolean cell : row) {
                sb.append(cell ? '1' : '0');
            }
            sb.append("\n");
        }
        return sb.toString();
    }
}
