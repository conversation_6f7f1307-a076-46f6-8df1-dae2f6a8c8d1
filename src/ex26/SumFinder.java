package ex26;

import ex26.numbers.MyNumber;

import java.util.List;

public class SumFinder {

    public <T extends MyNumber> MyNumber sum(T a, T b) {
        return a.add(b);
    }

    public <T extends MyNumber> MyNumber sum(List<T> numbers) {
        MyNumber sum = numbers.getFirst();
        for (int i = 1; i < numbers.size(); i++) {
            sum = sum.add(numbers.get(i));
        }
        return sum;
    }

}
