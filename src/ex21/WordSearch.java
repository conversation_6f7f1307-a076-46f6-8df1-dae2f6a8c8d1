package ex21;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class WordSearch {

    private final List<String> words = new ArrayList<>();

    public WordSearch() {
        words.addAll(loadFile());
    }

    public WordSearch(String testWords) {
        words.addAll(List.of(testWords.split(" ")));
    }

    public List<String> loadFile() {
        try {
            return Files.readAllLines(Paths.get("src/ex21/words.txt"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<String> find(String pattern) {
        return words.stream()
                .filter(word -> isMatch(word, pattern))
                .toList();
    }

    private boolean isMatch(String word, String pattern) {
        if (isDoubleWildStart(pattern)) {
            return isMatchFromEnd(word, pattern.substring(2));
        }

        int lastPost = word.length() - 1;

        for (int i = 0; i < pattern.length(); i++) {
            if (i > lastPost) {
                return false;
            } else if (pattern.charAt(i) == '*' && isSingleWildEnd(pattern) && i == lastPost) {
                return true;
            } else if (pattern.charAt(i) == '*' && isDoubleWildEnd(pattern)) {
                return true;
            } else if (pattern.charAt(i) != word.charAt(i)) {
                return false;
            }
        }

        return pattern.length() == word.length();
    }

    private boolean isMatchFromEnd(String word, String pattern) {
        if (word.length() < pattern.length()) {
            return false;
        }

        String ending = word.substring(word.length() - pattern.length());

        for (int i = 0; i < ending.length() ; i++) {
            if (ending.charAt(i) != pattern.charAt(i)) {
                return false;
            }
        }

        return true;
    }

    private boolean isSingleWildEnd(String pattern) {
        if (pattern.length() < 1) {
            return false;
        }

        return pattern.charAt(pattern.length() - 1) == '*' && !isDoubleWildEnd(pattern);
    }

    private boolean isDoubleWildStart(String pattern) {
        if (pattern.length() < 2) {
            return false;
        }

        return pattern.charAt(0) == '*' && pattern.charAt(1) == '*';
    }

    private boolean isDoubleWildEnd(String pattern) {
        if (pattern.length() < 2) {
            return false;
        }

        return pattern.charAt(pattern.length() - 1) == '*'
                && pattern.charAt(pattern.length() - 2) == '*';
    }

}