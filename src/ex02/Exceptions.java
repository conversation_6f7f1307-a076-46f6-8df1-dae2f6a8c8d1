package ex02;

public class Exceptions {

    private FileReader fileReader;

    public Exceptions(FileReader fileReader) {
        this.fileReader = fileReader;
    }

    public String main() throws Throwable {
        // throw new Throwable(); // delete this

        try {
            return readData();
        } catch (Throwable t) {
            throw new MyException3(t);
        }

        // call this.sub() and return result. Should not use fileReader directly.
    }

    public String sub() {
        // return ""; // delete this

        try {
            return readData();
        } catch (Throwable t) {
            throw new RuntimeException(t);
        }

        // call this.readData() and return result. Should not use fileReader directly.
    }

    public String readData() throws MyException2 {
        // fileReader.readFile(); // comment this in

        try {
            return fileReader.readFile();
        } catch (Throwable t) {
            throw new MyException2(t);
        }

        // throw new RuntimeException(); // delete this
    }
}