package ex27;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class NumberRepository {

    private String[] simpleNumberData = "2, 4, 6, 2, 9, 10".split(", ");
    private String[] scientificNumberData = "2e3, 2e4, 6e3".split(", ");

    public List<SimpleNumber> getSimpleNumbers() {
        return Arrays.stream(simpleNumberData)
                .map(each -> new SimpleNumber(Integer.parseInt(each)))
                .toList();
    }

    public List<ScientificNumber> getScientificNumbers() {
        return Arrays.stream(scientificNumberData)
                .map(pair -> pair.split("e"))
                .map(pair -> new int[] { Integer.parseInt(pair[0]),
                                         Integer.parseInt(pair[1])})
                .map(pair -> new ScientificNumber(pair[0], pair[1]))
                .toList();
    }

    public List<Number> getAllNumbers() {
        List<Number> list = new ArrayList<>();
        list.addAll(getSimpleNumbers());
        list.addAll(getScientificNumbers());
        return list;
    }

}
