package ex26.numbers;

public class DecNumber extends MyNumber {
    private Integer value;

    public DecNumber(int value) {
        this.value = value;
    }

    @Override
    public MyNumber add(MyNumber addend) {
        return new DecNumber(value + addend.intValue());
    }

    @Override
    public Integer intValue() {
        return value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    @Override
    public int compareTo(MyNumber o) {
        return this.value.compareTo(o.intValue());
    }
}
