package ex23;

public class ArrayMap {

    public Entry[] elements;

    private int nextIndex = 0;

    private int size = 0;

    public ArrayMap(int capacity) {
        elements = new Entry[capacity];
    }

    public void add(String key, int value) {
        int existingIndex = getExistingIndex(key);
        if (existingIndex >= 0) {
            elements[existingIndex] = new Entry(key, value);
            return;
        }


        elements[nextIndex++] = new Entry(key, value);
        nextIndex = nextIndex % elements.length;

        size++;
        size = Math.min(size, elements.length);
    }

    public Integer size() {
        return size;
    }

    private int getExistingIndex(String key) {
        int count = 0;
        for (Entry element : elements) {
            if (element != null && element.key.equals(key)) {
                return count;
            }
            count++;
        }

        return -1;
    }

    public int get(String key) {
        for (Entry element : elements) {
            if (element != null && element.key.equals(key)) {
                return element.value;
            }
        }

        throw new IllegalStateException("no such key: " + key);
    }

    public Integer[] values() {
        Integer[] result = new Integer[size];
        int count = 0;
        for (Entry element : elements) {
            if (element != null) {
                result[count++] = element.value;
            }
        }
        return result;
    }
}
