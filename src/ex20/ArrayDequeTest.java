package ex20;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.CoreMatchers.hasItems;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;

public class ArrayDequeTest {

    @Test
    @Points(4)
    public void elementsCanBeAddedToTheEnd() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addLast(1);
        deque.addLast(2);

        assertThat(deque.removeLast(), is(2));
        assertThat(deque.removeLast(), is(1));
    }

    @Test
    @Points(4)
    public void canSeeTheContentsOfDeque() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addLast(1);
        deque.addLast(2);
        deque.addLast(3);

        assertThat(asList(deque.getContents()), hasItems(1, 2, 3));
    }

    @Test
    @Points(4)
    public void elementsCanBeRemovedFromTheBeginning() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addLast(1);
        deque.addLast(2);

        assertThat(deque.removeFirst(), is(1));
        assertThat(deque.removeFirst(), is(2));

        assertThat(deque.getContents().length, is(0));
    }

    @Test
    @Points(7)
    public void elementsCanBeAddedToTheBeginning() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addFirst(1);
        deque.addFirst(2);
        deque.addFirst(3);

        assertThat(asList(deque.getContents()), hasItems(3, 2, 1));
    }

    @Test
    @Points(2)
    public void throwsWhenNoMoreElements() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addLast(1);
        deque.addFirst(4);
        deque.addLast(6);

        deque.removeFirst();
        deque.removeLast();
        deque.removeFirst();

        assertThrows(deque::removeFirst, IllegalStateException.class);
    }

    @Test
    @Points(4)
    public void canGetElementsInSorterOrder() {
        ArrayDeque deque = new ArrayDeque(10);

        deque.addLast(1);
        deque.addFirst(4);
        deque.addLast(6);
        deque.addLast(0);
        deque.addFirst(4);
        deque.addLast(8);

        assertThat(asList(deque.getSortedContents()),
                hasItems(0, 1, 4, 4, 6, 8));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {

        String path = "src/ex1/" + ArrayDeque.class.getSimpleName() + ".java";

        String source = Files.readString(Paths.get(path));

        assertThat(source, not(containsString("java.util")));
        assertThat(source, not(containsString("arraycopy")));
    }

    private List<Integer> asList(int[] array) {
        return Arrays.stream(array).boxed().toList();
    }

    private void assertThrows(Runnable code, Class<? extends Exception> expected) {
        try {
            code.run();
        } catch (Exception actual) {
            if (actual.getClass() != expected) {
                throw new AssertionError("Unexpected exception: " + actual);
            }

            return;
        }

        throw new AssertionError("Should throw: " + expected);
    }


}