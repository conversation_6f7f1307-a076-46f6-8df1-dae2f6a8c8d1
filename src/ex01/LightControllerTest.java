package ex01;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class LightControllerTest {

    @Test
    @Points(3)
    public void controllerHasStringRepresentation() {
        LightController controller = new LightController();

        List.of(new LightOff(), new LightOff(),
                new LightOn(), new LightOff(),
                new LightOff()).forEach(controller::addLight);

        assertThat(controller.asString(), is("XXOXX"));
    }

    @Test
    @Points(5)
    public void canTurnLightsOn() {
        LightController controller = getSampleLightController();
                                     // defined below

        controller.turnOn(4);

        assertThat(controller.asString(), is("XXXXO"));
    }

    @Test
    @Points(7)
    public void canToggleLights() {
        LightController controller = getSampleLightController();

        controller.toggle(1);
        controller.toggle(4);

        assertThat(controller.asString(), is("XOXXO"));

        controller.toggle(1);

        assertThat(controller.asString(), is("XXXXO"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() {
        Field[] fields = LightController.class.getDeclaredFields();

        assertThat(fields.length, is(1));

        assertThat(fields[0].getGenericType().getTypeName(),
                is("java.util.List<ex1.Light>"));

        assertThat(LightOn.class.getDeclaredFields().length, is(0));
        assertThat(LightOff.class.getDeclaredFields().length, is(0));
    }

    private LightController getSampleLightController() {
        LightController controller = new LightController();

        List.of(new LightOff(), new LightOff(),
                new LightOff(), new LightOff(),
                new LightOff()).forEach(controller::addLight);

        return controller;
    }
}
