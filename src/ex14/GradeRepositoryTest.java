package ex14;

import org.junit.Test;
import runner.Points;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class GradeRepositoryTest {

    @Test
    @Points(3)
    public void repositoryProvidesFullNames() {

        GradeRepository repo = new GradeRepository();

        List<String> fullNames = repo.getFullNames();

        assertThat(fullNames.size(), is(4));

        assertThat(fullNames, containsInAny<PERSON>rder(
                "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"));
    }

    @Test
    @Points(4)
    public void repositoryProvidesFullNamesSortedByFirstName() {

        GradeRepository repo = new GradeRepository();

        List<String> fullNames = repo.getFullNamesSortedByFirstName();

        assertThat(fullNames.size(), is(4));

        assertThat(fullNames, contains(
                "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"));
    }

    @Test
    @Points(6)
    public void repositoryProvidesFullNamesSortedByLastName() {

        GradeRepository repo = new GradeRepository();

        List<String> fullNames = repo.getFullNamesSortedByLastName();

        assertThat(fullNames.size(), is(4));

        assertThat(fullNames, contains(
                "Bob Brown", "David Doe", "Alice Smith", "Carol White"));
    }

    @Test
    @Points(2)
    public void repositoryProvidesFullNamesWithHighestGrade() {

        GradeRepository repo = new GradeRepository();

        List<String> fullNames = repo.getFullNamesWithHighestGrade();

        assertThat(fullNames.size(), is(4));

        assertThat(fullNames, containsInAnyOrder(
                "Bob Brown 5", "David Doe 4",
                "Alice Smith 5", "Carol White 3"));
    }


}
