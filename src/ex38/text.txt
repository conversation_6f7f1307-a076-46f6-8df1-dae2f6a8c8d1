 4p - 3
 7p - 1
10p - 2
17p - 4
18p - 1
25p - 9


  Ülesanne 1 (25 punkti)

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.
      Testid on järjestatud ülesande keerukuse järjekor<PERSON>. Seega on soovitatav
      kirjutada alguses vaid kood, mis esimese testi läbib jne.

  Paketis ex1 on klass CompactMatrix ja testid selle käitumise kontrollimiseks.
  Tavaline maatriks (nt. int[3][5]) on fikseeritud suurusega ja kasutab sama palju
  mälu (read * veerud) olenemata sellest, kui palju seal elemente sees on.
  CompactMatrix kujutab maatriksit, mis hoiab selles olevat infot kompaktsel kujul.
  Peab hoidma infot, selle kohta, mis positsioonil väärtus olemas on.

  Pole vaja arvestada juhuga, kus algsest mahust (CAPACITY = 10) väheks jääb.

  CompactMatrix käitumine on järgmine.

  getElementCount() - ütleb, mitu elementi on lisatud.

  add(row, col, value) - lisab elemendi value reale row ja veerule col.
                         kui sellel positsioonil on väärtus olemas, siis
                         kirjutatakse olemasolev väärtus üle.

  getRowSum(row) - tagastab sellel real olevate elementide summa.

  toString() - tagastab maatriksis oleva info sõne kujul.
               Nt. [(1;0)=6, (5;2)=9] - real 1, veerul 0 on väärtus 6 ja
                                        real 5, veerul 2 on väärtus 9.

  toOrdinaryMatrix() - tagastab maatriksi väärtuse tavakujul (Ineger[][]).

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi CompactMatrix ja uusi faile lisada ei või.
    - Peate kasutama olemasolevaid välju ja uusi välju lisada ei või.
    - Kood peab läbima testid klassist CompactMatrixTest.
