Ülesanne 2 (17 punkti)

  Klass ConfigLoader loeb konfiguratsiooni info etteantud failist. Failis
  data.properties on info, mida ülesandes kasutatakse.

  Faili lugemisel peate kasutama klassi MyFileInputStream. See pärineb klassist
  FileInputStream ja omab sama käitumist. <PERSON><PERSON><PERSON> on lisatud testimiseks vajalik
  lisaloogika.

  Pärast failist info lugemist tuleb fail kinni panna.

  Meetod getValue() võtab sisse võtme ja tagastab sellele vastava
  väärtuse etteantud konfiguratsiooni failist. Kui vastavat väärtust ei ole,
  siis visatakse erind Exception1.

  Kui proovitakse luua ConfigLoader klassi failile mida ei eksisteeri,
  peaks visatama erind Exception2.

  Kui toimub viga failist lugemise käigus, peaks visatama erind Exception3.

  Kui toimub eelpool nimetamata viga, peaks seesama viga välja visatama.

  Muuta võite klasse ConfigLoader.java. Uusi klasse luua ei tohi.
