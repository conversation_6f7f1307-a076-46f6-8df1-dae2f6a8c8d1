package ex41;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.Points;
import runner.PointsCounterExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ArrayTraverseTest {

    @Test
    @Points(2)
    public void handlesStepsOfOne() {

        int[] array = { 1, 1, 1, 1 };

        assertThat(ArrayTraverse.canBeTraversed(array)).isEqualTo(true);

        int[] array2 = { 1, 0, 1, 1 };

        assertThat(ArrayTraverse.canBeTraversed(array2)).isEqualTo(false);
    }

    @Test
    @Points(3)
    public void handlesLargerSteps() {

        int[] array = { 2, 0, 2, 0, 2 };

        assertThat(ArrayTraverse.canBeTraversed(array)).isEqualTo(true);

        int[] array2 = { 2, 0, 2, 0, 3, 0, 2, 0, 1 };

        assertThat(ArrayTraverse.canBeTraversed(array2)).isEqualTo(false);
    }

    @Test
    @Points(7)
    public void handlesNegativeSteps() {

        int[] array = { 3, 3, -1, -1, 2, 2, -1, 0 };

        assertThat(ArrayTraverse.canBeTraversed(array)).isEqualTo(true);

        int[] array2 = { 3, 3, -3, -1, 2, 2, -1, 0 };

        assertThat(ArrayTraverse.canBeTraversed(array2)).isEqualTo(false);
    }

    @Test
    @Points(8)
    public void handlesNegativeStepsWithLoops_doesNotHang() {
        int[] array = { 3, 3, -1, -1, 2, 2, -1, 0 };

        assertThat(ArrayTraverse.canBeTraversed(array)).isEqualTo(true);

        int[] array2 = { 1, 2, -1, -1, 0 };

        assertThat(ArrayTraverse.canBeTraversed(array2, true)).isEqualTo(false);
    }

    @Test
    @Points(5)
    public void handlesStepsForwardOrBackwards() {
        int[] array = { 2, 3, 1, 0, 3, 3, 0, 2, 0, 1 };

        assertThat(ArrayTraverse.canBeTraversed(array, true)).isEqualTo(true);

        int[] array2 = { 2, 3, 1, 1, 2, 3, 0, 0 };

        assertThat(ArrayTraverse.canBeTraversed(array2, true)).isEqualTo(false);
    }
}
