package ex25;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class ResultsStoreTest2 {

    @Test
    @Points(11)
    public void canFindBestResult() {

        // Results of Integer type

        ResultsStore<String, Integer> store = new ResultsStore<>();

        store.addResult("Alice", new Results<>(2, 9, 0));
        store.addResult("Bob", new Results<>(4, 2, 1));

        assertThat(store.getBestResult("Alice"), is(9));
        assertThat(store.getBestResult("Bob"), is(4));

        // Results of Double type

        ResultsStore<String, Double> store2 = new ResultsStore<>();

        store2.addResult("Carol", new Results<>(5.1, 4.0, 5.3));

        assertThat(store2.getBestResult("<PERSON>"), is(5.3));
    }
}
