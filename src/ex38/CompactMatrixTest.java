package ex38;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class CompactMatrixTest {

    @Test
    @Points(0)
    public void newMatrixHasZeroElements() {
        CompactMatrix matrix = new CompactMatrix();
        
        assertThat(matrix.getElementCount()).isEqualTo(0);
    }
    
    @Test
    @Points(4)
    public void canAddAndGetValues() {
        CompactMatrix matrix = new CompactMatrix();
        
        matrix.add(0, 0, 5);
        matrix.add(1, 2, 7);
        matrix.add(2, 1, 3);

        assertThat(matrix.getElementCount()).isEqualTo(3);

        assertThat(matrix.get(0, 0)).isEqualTo(5);
        assertThat(matrix.get(1, 2)).isEqualTo(7);
        assertThat(matrix.get(2, 1)).isEqualTo(3);
        assertThat(matrix.get(1, 1)).isNull();
        
        assertThat(matrix.getElementCount()).isEqualTo(3);
    }

    @Test
    @Points(3)
    public void canCalculateRowSums() {
        CompactMatrix matrix = new CompactMatrix();

        matrix.add(0, 1, 3);
        matrix.add(1, 2, 5);
        matrix.add(0, 3, 2);
        matrix.add(1, 0, 7);

        assertThat(matrix.getRowSum(0)).isEqualTo(5); // 3 + 2
        assertThat(matrix.getRowSum(1)).isEqualTo(12); // 5 + 7
    }

    @Test
    @Points(3)
    public void toStringReturnsInternalState() {
        CompactMatrix matrix = new CompactMatrix();

        matrix.add(1, 0, 6);
        matrix.add(1, 2, 9);
        matrix.add(5, 1, 7);

        assertThat(matrix.toString())
                .isEqualTo("[(1;0)=6, (1;2)=9, (5;1)=7]");
    }

    @Test
    @Points(7)
    public void canUpdateExistingValues() {
        CompactMatrix matrix = new CompactMatrix();
        
        matrix.add(0, 1, 5);
        assertThat(matrix.get(0, 1)).isEqualTo(5);

        assertThat(matrix.getElementCount()).isEqualTo(1);

        matrix.add(0, 1, 8); // Update
        assertThat(matrix.get(0, 1)).isEqualTo(8);

        assertThat(matrix.getElementCount()).isEqualTo(1);
    }

    @Test
    @Points(8)
    public void canConvertToOrdinaryMatrix() {
        CompactMatrix matrix = new CompactMatrix();

        matrix.add(0, 0, 1);
        matrix.add(0, 2, 3);
        matrix.add(1, 1, 5);
        matrix.add(3, 0, 7);

        Integer[][] ordinary = matrix.toOrdinaryMatrix();

        assertThat(ordinary).isEqualTo(new Integer[][] {
                { 1   , null, 3    },
                { null, 5   , null },
                { null, null, null },
                { 7   , null, null }
        });
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {
        String path = String.format("src/%s.java",
                CompactMatrix.class.getName().replaceAll("\\.", "/"));
                
        String source = Files.readString(Paths.get(path));
        
        assertThat(source).doesNotContain("java.util");
        assertThat(source).doesNotContain("Map");
        assertThat(source).doesNotContain("List");
    }
    
    @Test
    @NoPointsIfThisTestFails
    public void shouldHaveOnlyAllowedFields() {

        Field[] fields = CompactMatrix.class.getDeclaredFields();

        assertThat(fields.length).isEqualTo(5);

        List<Field> fieldsNotAllowed = Arrays.stream(fields)
                .filter(field -> !field.getType().equals(int.class))
                .filter(field -> !field.getType().equals(int[].class))
                .toList();


        assertThat(fieldsNotAllowed).isEmpty();
    }
}
