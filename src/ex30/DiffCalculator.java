package ex30;

public class DiffCalculator {

    private int[] lengths = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    public int daysBetween(MyDate start, MyDate end) {
        if (start.year() == end.year()) {
            return daysFromStartOfYear(end) - daysFromStartOfYear(start);
        }

        int days = daysInYear(start.year()) - daysFromStartOfYear(start);
        for (int i = start.year() + 1; i < end.year(); i++) {
            days += daysInYear(i);
        }
        days += daysFromStartOfYear(end);

        return days;
    }

    private int daysFromStartOfYear(MyDate date) {
        int sum = 0;
        for (int i = 1; i < date.month().ordinal() + 1; i++) {
            sum += daysInMonth(i, date.year());
        }
        sum += date.day();

        return sum;
    }

    private int daysInMonth(int month, int year) {
        return month == 2 && isLeapYear(year) ? 29 : lengths[month - 1];
    }

    private int daysInYear(int year) {
        return isLeapYear(year) ? 366 : 365;
    }

    private boolean isLeapYear(int year) {
        return year % 4 == 0 && year % 100 != 0 || year % 400 == 0;
    }

}
