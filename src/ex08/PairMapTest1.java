package ex08;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class PairMapTest1 {

    @Test
    @Points(5)
    public void pairMapStoresPairObjects() {

        PairMap<String, Integer> map = new PairMap<>();

        map.put("k1", 1, 2);

        Integer first = map.get("k1").getFirst();
        Integer second = map.get("k1").getSecond();

        assertThat(first, is(1));
        assertThat(second, is(2));
    }

}
