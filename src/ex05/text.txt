Ülesanne 1 (17 punkti)

  TODO: te<PERSON><PERSON>, et esimestes testides võib arvestada and<PERSON><PERSON> järjekorra erip<PERSON>raga.

  Kataloogis ex1 on klass NumberStore ja test selle testimiseks.

  NumberStore on klass, mis hoiab numbreid.

  Meetod add() lisab uue numbri. Ei pea arvestama juhuga, kui lisatakse üle 100 numbri.

  Meetod getStoredNumbers() tagastab sisestatud numbrid sorteerituna.

  Kirjutage vajalik kood, et testid tööle hakkaksid.

  Peate hakkama saama massiiviga ja dünaamilisi kollektsioone kasutada ei või.

  Muuta võite klassi NumberStore.java. Uusi klasse luua ei tohi.
