<PERSON><PERSON><PERSON> (25 punkti)

  Ülesande kood asub Paketis ex1.

  <PERSON><PERSON> on testid välja kommenteeritud, kuna muidu
  need ei kompileeruks. Kui kirjutate klasside Results ja ResultsStore vajalikud
  tagastustüübid ja tüübiparameetrid, peaks ka testid kompileeruma. Seega on
  teie esmane ü<PERSON>anne testid kompileerima saada.

  Testid on kolmes failis: ResultsTest.java, ResultsStoreTest1.java ja ResultsStoreTest2.java.

  NB! Muuta võite klasse Results.java ja ResultsStore.java ning nendes klassides võite muuta kõike.
      Piiranguks on see, et kui test sisse kommenteerida, siis peab kood kompileeruma.
      Uusi klasse luua ei tohi. Testides peate väljakommenteeritud koodi sisse kommenteerima.

  Klass Results on konteiner kolme samatüübilise elemendi hoidmiseks.
  Seda võiks kasutada näiteks tulemuste hoidmiseks võist<PERSON>, kus võistlejal on kolm
  katset millest parim loeb.

    new Results<Integer>(5, 4.5, 5.1) teeb uue objekti, mis hoiab kolme katse tulemusi.

  Selle osa kohta on test ResultsTest.java.

  Klass ResultStore on kollektsioon, mis seob identifikaatori ja sellele vastava tulemuse.
  Identifikaator määrab, millisele võistlejale antud tulemus kuulub. See võib olla näiteks
  võistleja nimi sõnena või ka võistleja objektina.

    ResultsStore<String, Integer> store = new ResultsStore<>();

  Loob uue hoidla (store), mille võtme tüüp on Sõne ja väärtuseks on täisarvulised tulemused.

    store.addResult("Alice", new Results<>(2, 9, 0));

  Lisab hoidlasse võistleja "Alice" tulemused.
  Selle osa kohta on test ResultsStoreTest1.java.

  Hoidla oskab leida võistleja parima tulemuse. Parim tulemus leitakse sorteerimise
  põhjal. Näiteks numbrite puhul on see suurim number.

  Selle osa kohta on test ResultsStoreTest2.java.
