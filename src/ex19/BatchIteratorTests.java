package ex19;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.empty;

public class BatchIteratorTests {

    @Test
    @Points(0)
    public void batchHasToStringMethod() {
        Batch batch = new Batch(1, 2);

        assertThat(batch.toString(), is("[1, 2]"));
    }

    @Test
    @Points(5)
    public void iteratorKnowsWhetherThereIsNextBatch() {
        Batch b1 = new Batch(1, 2);
        Batch b2 = new Batch(7, 0);

        BatchIterator it = new BatchIterator(b1, b2);

        assertThat(it.hasNextBatch(), is(true));

        it.nextBatch();

        assertThat(it.hasNextBatch(), is(true));

        it.nextBatch();

        assertThat(it.hasNextBatch(), is(false));
    }

    @Test
    @Points(15)
    public void iteratorReturnsBatchesInOrder() {
        Batch b1 = new Batch(1, 2);
        Batch b2 = new Batch(7, 0);
        Batch b3 = new Batch(3);

        BatchIterator it = new BatchIterator(b1, b2, b3);

        assertThat(it.nextBatch().toString(), is("[1, 2]"));

        assertThat(it.nextBatch().toString(), is("[7, 0]"));

        assertThat(it.nextBatch().toString(), is("[3]"));

        assertThrows(it::nextBatch, IllegalStateException.class);
    }

    @Test()
    @Points(15)
    public void iteratorReturnsElementsFromBatches() {
        Batch b1 = new Batch(1, 2);
        Batch b2 = new Batch(7, 0);
        Batch b3 = new Batch(3);

        BatchIterator it = new BatchIterator(b1, b2, b3);

        assertThat(it.nextElement(), is(1));
        assertThat(it.nextElement(), is(2));
        assertThat(it.nextElement(), is(7));
        assertThat(it.nextElement(), is(0));
        assertThat(it.nextElement(), is(3));
    }

    @Test()
    @Points(5)
    public void elementIteratorCanHandleEmptyBatches() {
        emptyBatchesAreValidElements();

        Batch b1 = new Batch(8, 2);
        Batch b2 = new Batch();
        Batch b3 = new Batch(6);

        BatchIterator it = new BatchIterator(b1, b2, b3);

        assertThat(it.nextElement(), is(8));
        assertThat(it.nextElement(), is(2));
        assertThat(it.nextElement(), is(6));
    }

    public void emptyBatchesAreValidElements() {
        Batch b1 = new Batch();
        Batch b2 = new Batch(8, 2);
        Batch b3 = new Batch();
        Batch b4 = new Batch();
        Batch b5 = new Batch(6);

        BatchIterator it = new BatchIterator(b1, b2, b3, b4, b5);

        assertThat(it.nextBatch().toString(), is("[]"));
        assertThat(it.nextBatch().toString(), is("[8, 2]"));
        assertThat(it.nextBatch().toString(), is("[]"));
        assertThat(it.nextBatch().toString(), is("[]"));
        assertThat(it.nextBatch().toString(), is("[6]"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void batchIteratorShouldHaveOnlyAllowedFields() {
        int listFieldCount = Arrays.stream(BatchIterator.class.getDeclaredFields())
                .filter(field -> field.getType().equals(List.class)).toList().size();

        assertThat(listFieldCount, is(1));

        List<Field> fieldsNotAllowed = Arrays.stream(BatchIterator.class.getDeclaredFields())
                .filter(field -> !field.getType().equals(List.class))
                .filter(field -> !field.getType().equals(int.class))
                .filter(field -> !field.getType().equals(Integer.class))
                .toList();

        assertThat(fieldsNotAllowed, is(empty()));
    }

    private void assertThrows(Runnable code, Class<? extends Exception> expected) {
        try {
            code.run();
        } catch (Exception actual) {
            if (actual.getClass() != expected) {
                throw new AssertionError("Unexpected exception: " + actual);
            }

            return;
        }

        throw new AssertionError("Should throw: " + expected);
    }

}