package ex08;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class PairTest {

    @Test
    @Points(5)
    public void canCreateAPairForArbitraryType() {

        Pair<Float> pair = new Pair<>(1f, 2f);

        Float first = pair.getFirst();
        Float second = pair.getSecond();

        assertThat(first, is(1f));
        assertThat(second, is(2f));

        assertThat(new Pair<>(1, 2).getFirst(), is(1));
        assertThat(new Pair<>(1, 2).getSecond(), is(2));
    }
}
