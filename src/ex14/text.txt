Ülesanne 1 (15 punkti)

  ex1.GradeRepository on klass, mis loeb failist infot tudengite ja nende hinnete kohta.

  Meetod getFullNames() tagastab listi tudengite täisnimedest (eesnimi + " " + perekonnanimi).

  Meetod getFullNamesSortedByFirstName() tagastab listi tudengite täisnimedest
  sorteerituna eesnime järgi.

  Meetod getFullNamesSortedByLastName() tagastab listi tudengite täisnimedest
  sorteerituna perekonnanime järgi.

  Meetod getFullNamesWithHighestGrade() tagastab listi tudengite täisnimedest
  millele on lisatud konkreetse isiku kõrgeim hinne.
  (eesnimi + " " + perekonnanimi + " " + hinne).

  Muuta võite klassi GradeRepository. Vajadusel võite uusi klasse juurde luua.
