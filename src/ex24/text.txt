Ülesanne 1 (25 punkti)

  Paketis ex1 on klass DimensionStore ja test selle testimiseks.

  NB!!! <PERSON><PERSON><PERSON> lõ<PERSON> on piirangud, mille eiramisel ülesannet ei arvestata.

  DimensionStore võimaldab hoida int tüüpi väärtusi. Väärtusi hoitakse massiivis.
  <PERSON><PERSON>ine on see, et andmete hoidmiseks kasutatakse mitmemõõtmelist massiivi.

  Klassil DimensionStore on järgmised meetodid

    add(<value>) - lisab uue elemendi.

    size() - ütleb, mitu elementi on lisatud.

    getLast() - tagastab viimase elemendi.

    values() - tagastab kõik elemendid masiivina.

    shift() - eemaldab esimese elemendi ja tagastab selle.

  Andmete hoidmiseks on väljad elements1d, elements2d või elements3d. Valige
  ise, millisel väljal andmeid hoiate. elements1d võimaldab hoida kuni 4 elementi
  ja sellest piisab esimeste testide läbimiseks. Viimane test eeldab, et
  teie lahendus suudab hoida 24 elementi ja seega selle testi läbimiseks peate hoidma
  andmeid väljal elements3d.

  Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
  kirjutada alguses vaid kood, mis esimese testi läbib jne.

  NB! Piirangud, mille eiramisel ülesannet ei arvestata.
    - Muuta võite vaid klassi DimensionStore. Uusi klasse luua ei tohi.
    - Klassile DimensionStore võite lisada ainult täisarv tüüpi välju.
    - Kood peab töötama ka siis, kui konstantides olevad väärtused ära muuta.
      Nt. kui muuta X_DIMENSION väärtus 8-ks, siis peaks ka maksimaalne elementide arv kahekordistuma.
    - Kood peab läbima testid klassist DimensionStoreTests.
    - Kui @NoPointsIfThisTestFails märgisega test läbi ei lähe, siis lahendust ei arvestata.
