package ex39;

import ex39.values.NumericValue;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class ValueHandler5<T> {

    private Map<Object, Consumer<T>> handlers = new HashMap<>();

    public void registerHandler(Object valueType,
                                Consumer<T> handler) {

        handlers.put(valueType, handler);
    }

    public void handleAll(List<? extends T> values) {
        for (T value : values) {
            Class<?> valueClass = value.getClass();

            Consumer<T> handler = handlers.get(valueClass);
            if (handler != null) {
                handler.accept(value);
            }
        }

    }
}
