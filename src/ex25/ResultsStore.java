package ex25;

import java.util.*;

public class ResultsStore<T, U extends Comparable<U>> {

    private Map<T, Results<U>> map = new HashMap<>();

    public void addResult(T key, Results<U> results) {
        map.put(key, results);
    }

    public List<U> getResults(T key) {
        Results<U> results = map.get(key);
        return List.of(results.r1(), results.r2(), results.r3());
    }

    public U getBestResult(T key) {
        Results<U> results = map.get(key);
        List<U> list = new ArrayList<>(List.of(results.r1(), results.r2(), results.r3()));
        Collections.sort(list);
        return list.get(2);
    }
}
