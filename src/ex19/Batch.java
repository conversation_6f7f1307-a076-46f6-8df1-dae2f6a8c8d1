package ex19;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Batch {

    private final List<Integer> elements;

    public Batch(Integer ... elements) {
        this.elements = Arrays.asList(elements);
    }

    @Override
    public String toString() {
        return elements.toString();
    }

    public List<Integer> getElements() {
        return new ArrayList<>(elements);
    }
}
