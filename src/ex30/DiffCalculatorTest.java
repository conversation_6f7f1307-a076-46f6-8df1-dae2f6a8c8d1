package ex30;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class DiffCalculatorTest {

    @Test
    @Points(2)
    public void sameMonth() {
        var start = new MyDate(3, Month.JANUARY, 2024);
        var end = new MyDate(15, Month.JANUARY, 2024);

        int diff = new DiffCalculator().daysBetween(start, end);

        assertThat(diff, is(12));
    }

    @Test
    @Points(3)
    public void differentMonth() {
        var start = new MyDate(13, Month.MARCH, 2024);
        var end = new MyDate(2, Month.AUGUST, 2024);

        int diff = new DiffCalculator().daysBetween(start, end);

        assertThat(diff, is(142));
    }

    @Test
    @Points(10)
    public void handlesLeapYears() {
        var start = new MyDate(3, Month.JANUARY, 2024);
        var end = new MyDate(2, Month.MARCH, 2024);

        int diff = new DiffCalculator().daysBetween(start, end);

        assertThat(diff, is(59));

        var start2 = new MyDate(3, Month.JANUARY, 2023);
        var end2 = new MyDate(2, Month.MARCH, 2023);

        int diff2 = new DiffCalculator().daysBetween(start2, end2);

        assertThat(diff2, is(58));
    }

    @Test
    @Points(10)
    public void multipleYears() {
        var start = new MyDate(5, Month.MARCH, 2020);
        var end = new MyDate(20, Month.OCTOBER, 2024);

        int diff = new DiffCalculator().daysBetween(start, end);

        assertThat(diff, is(1690));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {

        String path = "src/ex30/" + DiffCalculator.class.getSimpleName() + ".java";

        String source = Files.readString(Paths.get(path));

        assertThat(source, not(containsString("java.util")));
        assertThat(source, not(containsString("java.time")));
    }

    // remove
    private static long getExpected(MyDate start, MyDate end) {
        LocalDate dStart = LocalDate.of(start.year(), convert(start), start.day());
        LocalDate dEnd = LocalDate.of(end.year(), convert(end), end.day());

        return dEnd.toEpochDay() - dStart.toEpochDay();
    }

    private static java.time.Month convert(MyDate myDate) {
        return java.time.Month.of(myDate.month().ordinal() + 1);
    }
}