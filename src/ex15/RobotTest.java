package ex15;

import org.junit.Test;
import runner.Points;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class RobotTest {

    @Test
    @Points(10)
    public void updatesPositionWhileMoving() {
        Robot robot = new Robot();

        robot.moveNorth(1);

        assertPosition(robot, 1, 0);

        robot.moveWest(2);

        assertPosition(robot, 1, -2);

        robot.moveSouth(1);

        assertPosition(robot, 0, -2);

        robot.moveEast(2);

        assertPosition(robot, 0, 0);
    }

    @Test
    @Points(10)
    public void retractSingleOperation() {
        Robot robot = new Robot();

        robot.moveNorth(1);

        assertPosition(robot, 1, 0);

        robot.retractLast();

        assertPosition(robot, 0, 0);
    }

    @Test
    @Points(16)
    public void retractMultipleOperations() {
        Robot robot = new Robot();

        robot.moveNorth(1);
        robot.moveEast(3);
        robot.moveNorth(2);
        robot.moveWest(4);
        robot.moveSouth(1);

        assertPosition(robot, 2, -1);

        robot.retractLast();
        robot.retractLast();
        robot.retractLast();

        assertPosition(robot, 1, 3);
    }

    private void assertPosition(Robot robot, int x, int y) {
        assertThat("x position should be " + x,
                robot.getPositionX(), is(x));
        assertThat("y position should be " + y,
                robot.getPositionY(), is(y));
    }
}