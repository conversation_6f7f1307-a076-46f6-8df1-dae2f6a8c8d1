package ex29;

import java.util.LinkedList;

public class Camera {

    private int positionH = 0;
    private int positionV = 0;

    private LinkedList<Runnable> undoOperations;

    public Camera() {
        // delete this
        undoOperations = new LinkedList<>();
    }

    public int getPosition(Axes axes) {
        return axes == Axes.HORIZONTAL ? positionH : positionV;
    }

    public void cancelLastAction() {
        undoOperations.removeLast().run();

        undoOperations.removeLast();
    }

    public void move(Axes axes, Direction direction, int degrees) {
        System.out.printf("moving %s %s %d degrees\n", axes, direction, degrees);

        int value = direction == Direction.CLOCKWISE ? degrees : -degrees;
        Direction oppositeDirection = direction == Direction.CLOCKWISE
                ? Direction.COUNTERCLOCKWISE : Direction.CLOCKWISE;

        if (axes == Axes.HORIZONTAL) {
            positionH += value;
            undoOperations.add(() -> move(Axes.HORIZONTAL, oppositeDirection, degrees));
        } else {
            positionV += value;
            undoOperations.add(() -> move(Axes.VERTICAL, oppositeDirection, degrees));
        }

    }
}
