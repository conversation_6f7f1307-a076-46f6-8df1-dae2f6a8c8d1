total 37
max (40): 4

0p: 9 (koos mitte esitaja<PERSON>ga)
2p: 2
5p: 3
15p: 6
27p: 5
37p: 8
40p: 4

Hinnang: he<PERSON> <PERSON><PERSON><PERSON> on vaja arvutada nii suurte numbritega, et <PERSON> tüübist jääb väheks.
  Double tüüp pole aga piisavalt täpne. <PERSON><PERSON> on kirjutada kood,
  mis võimaldab suurte numbritega arvutamist. Numbrite liitmise algoritm
  peaks algkoolist meeles olema.

  Paketis ex1 on klass MyLargeNumber. Selle käitumist kirjeldavad testid
  klassist MyLargeNumberTests.

  Kui palju aega oleks, saaksite kindlasti kõik testid tööle aga antud juhul see nii pole.
  Seega on soovitatav alustada erijuhtudest ja kui need valmis, siis rakendust täiendada.

  Testid on järjestatud ülesande keerukuse järjekorras. Näiteks esimese testi
  läbimiseks piisab, kui teie kood saab hakkama ühekohaliste numbritega.

  Muuta võite klassi MyLargeNumber ja uusi klasse lisada ei või.
  Klasse BigInteger või muid selliseid Javaga kaasas olevaid klasse
  loomulikult kasutada ei tohi.
