package ex37;

import java.util.LinkedList;

public class VersionedDocument {

    private String content = "";

    private static final Modification MARKER = t -> t;

    private LinkedList<Modification> modifications = new LinkedList<>();

    public String getContent() {
        return content;
    }

    public void append(String textToAppend) {
        Modification m = currentContent -> currentContent + textToAppend;

        content = m.apply(content);

        modifications.add(m);
    }

    public void replace(String oldText, String newText) {
        Modification m = currentContent -> currentContent.replace(oldText, newText);

        content = m.apply(content);

        modifications.add(m);
    }

    public void delete(int startIndex, int endIndex) {
        Modification m = text -> text.substring(0, startIndex) +
                                     text.substring(endIndex);

        content = m.apply(content);

        modifications.add(m);
    }

    public void createSnapshot() {
        modifications.add(MARKER);
    }

    public String getVersion(int no) {

        String result = "";

        int counter = 0;

        for (Modification modification : modifications) {
            result = modification.apply(result);

            if (counter++ >= no) {
                break;
            }
        }

        return result;
    }

    public String getSnapshot(int snapshotIndex) {
        String result = "";

        int counter = 0;

        for (Modification modification : modifications) {
            if (modification == MARKER) {
                counter++;
            }

            if (counter > snapshotIndex) {
                break;
            }

            result = modification.apply(result);
        }

        return result;
    }
}
