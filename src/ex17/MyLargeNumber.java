package ex17;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class MyLargeNumber {

    private List<Integer> digits = new ArrayList<>();

    public MyLargeNumber(String number) {
        for (char c : number.toCharArray()) {
            digits.add(Integer.parseInt(Character.toString(c)));
        }
    }

    private MyLargeNumber(List<Integer> digits) {
        this.digits = digits;
    }

    public MyLargeNumber add(MyLargeNumber addend) {
        List<Integer> resultDigits = new ArrayList<>();

        int size = Math.max(digits.size(), addend.digits.size());
        List<Integer> digits1 = pad(digits, size);
        List<Integer> digits2 = pad(addend.digits, size);

        int carry = 0;
        for (int i = size - 1; i >= 0; i--) {

            int sum = digits1.get(i) + digits2.get(i) + carry;

            if (sum > 9) {
                carry = 1;
                sum = sum % 10;
            } else {
                carry = 0;
            }

            resultDigits.add(0, sum);
        }

        if (carry == 1) {
            resultDigits.add(0, 1);
        }

        return new MyLargeNumber(resultDigits);
    }

    private List<Integer> pad(List<Integer> digits, int size) {
        List<Integer> result = new ArrayList<>(digits);
        while (result.size() < size) {
            result.add(0, 0);
        }

        return result;
    }

    private int getDigitAtPos(List<Integer> digits, int pos) {
        return pos < digits.size()
                ? digits.get(pos)
                : 0;
    }

    private List<Integer> reverse(List<Integer> resultDigits) {
        List<Integer> result = new ArrayList<>();
        for (Integer digit : resultDigits) {
            result.add(0, digit);
        }
        return result;
    }

    public MyLargeNumber multiply(int multiplier) {
        MyLargeNumber result = new MyLargeNumber("0");
        for (int i = 0; i < multiplier; i++) {
            result = result.add(this);
        }
        return result;
    }

    public MyLargeNumber power(int exponent) {
        MyLargeNumber result = new MyLargeNumber("1");
        for (int i = 0; i < exponent; i++) {
            result = result.multiply(Integer.parseInt(this.toString()));
        }
        return result;
    }

    @Override
    public String toString() {
        return digits.stream()
                .map(Object::toString)
                .collect(Collectors.joining());
    }
}
