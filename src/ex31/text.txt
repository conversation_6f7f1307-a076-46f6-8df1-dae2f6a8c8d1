Ülesanne 1 (25 punkti)

  NB! <PERSON><PERSON><PERSON> lõpus on tingimused, mille eiramisel ei lähe tulemus arvesse.

  Kataloogis ex1 on klass Board, mis kujutab tabelit ridade ja veergudega.
  Tabelis hoitakse boolean väärtusi. Meetod getChunkSize(row, col) ütleb
  mitu kõrvuti tõese väärtusega ruutu antud positsioonil ja selle kõrval asub.

  <PERSON><PERSON><PERSON><PERSON><PERSON> on ruudud, mis asuvad üksteisest vahetult üleval või all. Diagonaalis
  olevad ruudud pole kõrvuti. Kui antud kordinaadil asuv ruut ise pole tõene,
  siis ei oma naabrid tähtsust.

  Näiteks:

       0    1    2    3    4
    +----+----+----+----+----+
  0 |    |    |    |    |    |
    +----+----+----+----+----+
  1 |    |    |    |    | ██ |
    +----+----+----+----+----+
  2 | ██ | ██ |    |    | ██ |
    +----+----+----+----+----+
  3 |    |    |    |    | ██ |
    +----+----+----+----+----+

  getChunkSize(0, 0) = 0
  getChunkSize(1, 2) = 0
  getChunkSize(1, 1) = 0

  getChunkSize(2, 0) = 2
  getChunkSize(2, 1) = 2

  getChunkSize(1, 4) = 3
  getChunkSize(2, 4) = 3
  getChunkSize(3, 4) = 3

  NB! Tingimiused, mille eiramisel ei lähe tulemus arvesse.
    - Muuta võite klassi Board.java. Uusi klasse luua ei tohi.
    - Tabeli suurus antakse konstruktoris ette ja see tohi koodi jäigalt
      sisse kirjutatud (hard coded) olla.
    - Kood peab läbima testid klassist BoardTest.
