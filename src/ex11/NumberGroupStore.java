package ex11;

import java.util.Comparator;
import java.util.LinkedList;

public class NumberGroupStore {

    private LinkedList<NumberGroup> groups = new LinkedList<>();

    public void addGroup(int a, int b, int c) {
        groups.add(new NumberGroup(a, b, c));
    }

    public int getLastA() {
        return groups.getLast().a;
    }

    public int getLastB() {
        return groups.getLast().b;
    }

    public int getLastC() {
        return groups.getLast().c;
    }

    public void removeLastGroup() {
        groups.removeLast();
    }

    public void sortByA() {
        groups.sort(Comparator.comparingInt(a -> a.a));
    }

    public void sortByB() {
        groups.sort(Comparator.comparingInt(a -> a.b));
    }

    public void sortByC() {
        groups.sort(Comparator.comparingInt(a -> a.c));
    }
}
