package ex08;

import java.util.HashMap;
import java.util.Map;

public class PairMap<T, U extends Comparable<U>> {

    private Map<T, Pair<U>> map = new HashMap<>();

    public void put(T key, U first, U second) {
        Pair<U> pair = first.compareTo(second) < 0
            ? new Pair<>(first, second)
            : new Pair<>(second, first);

        map.put(key, pair);
    }

    public Pair<U> get(T key) {
        return map.get(key);
    }
}
