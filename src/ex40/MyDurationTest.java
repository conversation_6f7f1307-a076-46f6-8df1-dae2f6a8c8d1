package ex40;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static ex40.MyDuration.Unit.*;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class MyDurationTest {

    @Test
    @Points(1)
    public void createFromString() {
        MyDuration duration = new MyDuration("04:01:05");

        assertThat(duration.toString()).isEqualTo("04:01:05");
    }

    @Test
    @Points(3)
    public void getMinutes() {
        MyDuration duration = new MyDuration("01:16:05");

        assertThat(duration.get(MINUTES)).isEqualTo(16);
    }

    @Test
    @Points(2)
    public void getDays() {
        MyDuration duration = new MyDuration("03:01:05");

        assertThat(duration.get(HOURS)).isEqualTo(3);
    }

    @Test
    @Points(5)
    public void addMinutes() {
        MyDuration duration = new MyDuration("02:24:05");

        assertThat(duration.add(6, MINUTES).toString())
                .isEqualTo("02:30:05");
    }

    @Test
    @Points(3)
    public void floorToHours() {
        MyDuration duration = new MyDuration("02:24:05");

        assertThat(duration.floor(HOURS).toString())
                .isEqualTo("02:00:00");
    }

    @Test
    @Points(2)
    public void crateFromSecondsSimple() {
        MyDuration duration = new MyDuration(50);

        assertThat(duration.toString()).isEqualTo("00:00:50");
    }

    @Test
    @Points(4)
    public void crateFromSecondsFull() {
        assertThat(new MyDuration(25265).toString())
                .isEqualTo("07:01:05");
    }

    @Test
    @Points(5)
    public void addOtherMyDuration() {
        assertThat(new MyDuration("18:01:05").add(new MyDuration("3:01:05")))
                .isEqualTo(new MyDuration("21:02:10"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {
        String path = String.format("src/%s.java",
                MyDuration.class.getName().replaceAll("\\.", "/"));

        String source = Files.readString(Paths.get(path));

        assertThat(source).doesNotContain("java.util");
        assertThat(source).doesNotContain("java.time");
    }


}
