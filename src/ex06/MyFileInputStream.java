package ex06;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

public class MyFileInputStream extends FileInputStream {

    public static boolean isOpen;
    public static boolean throwOnRead;
    public static RuntimeException throwOnRead2;

    public MyFileInputStream(File file) throws FileNotFoundException {
        super(file);

        isOpen = true;
    }

    public MyFileInputStream(String name) throws FileNotFoundException {
        this(new File(name));
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        if (throwOnRead) {
            throw new IOException("error on reading file");
        }
        if (throwOnRead2 != null) {
            throw throwOnRead2;
        }

        return super.read(b, off, len);
    }

    @Override
    public void close() throws IOException {
        super.close();

        isOpen = false;
    }
}
