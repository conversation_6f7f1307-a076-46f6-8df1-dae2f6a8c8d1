package ex01;

import java.util.*;
import java.util.stream.Collectors;

public class LightController {

    private List<Light> lights = new ArrayList<>();

    public void addLight(Light light) {
        lights.add(light);
    }

    public String asString() {
        return lights.stream().map(Object::toString)
                .collect(Collectors.joining());
    }

    public void turnOn(int index) {
        lights.set(index, new LightOn());
    }

    public void toggle(int index) {
        lights.set(index, lights.get(index).toggle());
    }


}
