package ex10;

import java.util.ArrayList;
import java.util.List;

public class BatchStore {

    private List<List<? extends Number>> batches = new ArrayList<>();

    public void addBatch(List<? extends Number> batch) {
        batches.add(batch);
    }

    public int getSize() {
        return batches.size();
    }

    public List<?> getBatchWithMaxSum() {
        List<? extends Number> batch = null;
        Integer max = null;
        for (List<? extends Number> numbers : batches) {
            int sum = numbers.stream().mapToInt(Number::intValue).sum();
            if (max == null || max < sum) {
                max = sum;
                batch = numbers;
            }
        }

        return batch;
    }

    public Integer getSizeOfLargestBatch() {
        Integer maxSize = null;
        for (List<?> numbers : batches) {
            if (maxSize == null || maxSize < numbers.size()) {
                maxSize = numbers.size();
            }
        }

        return maxSize;
    }
}
