package ex39;

import ex39.values.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.WarningChecker;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ValueHandler5Test {

    @Test
    @Points(7)
    public void canRegisterHandlersForListOfMessages() {
        List<NumericValue> integers = new ArrayList<>();
        List<NumericValue> doubles = new ArrayList<>();

        ValueHandler5<NumericValue> handler = new ValueHandler5<>();

        handler.registerHandler(IntegerValue.class, integers::add);
        handler.registerHandler(DoubleValue.class, doubles::add);

        List<IntegerValue> integerValues = List.of(new IntegerValue(7), new IntegerValue(4));
        List<DoubleValue> doubleValues = List.of(new DoubleValue(2.0), new DoubleValue(1.8));

        handler.handleAll(integerValues);
        handler.handleAll(doubleValues);

        assertThat(integers.toString()).isEqualTo("[7, 4]");
        assertThat(doubles.toString()).isEqualTo("[2.0, 1.8]");
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldCompileWithoutWarnings() {
        assertThat(WarningChecker.hasWarnings(ValueHandler5.class)).isFalse();
        assertThat(WarningChecker.hasWarnings(ValueHandler5Test.class)).isFalse();
    }

}
