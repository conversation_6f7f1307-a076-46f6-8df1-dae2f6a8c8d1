package ex11;

import org.junit.Test;
import runner.Points;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.is;

public class NumberGroupStoreTest {

    @Test
    @Points(3)
    public void canGetLastGroupMembers() {
        NumberGroupStore store = new NumberGroupStore();

        store.addGroup(1, 2, 3);

        assertThat(store.getLastA(), is(1));
        assertThat(store.getLastB(), is(2));
        assertThat(store.getLastC(), is(3));
    }

    @Test
    @Points(6)
    public void canRemoveLastGroup() {
        NumberGroupStore store = new NumberGroupStore();

        store.addGroup(1, 2, 3);

                   // defined below
        assertThat(getLastGroup(store), contains(1, 2, 3));

        store.addGroup(4, 5, 6);

        assertThat(getLastGroup(store), contains(4, 5, 6));

        store.removeLastGroup();

        assertThat(getLastGroup(store), contains(1, 2, 3));
    }

    @Test
    @Points(8)
    public void canSortGroupsByGroupMember() {
        NumberGroupStore store = new NumberGroupStore();

        store.addGroup(6, 1, 8);
        store.addGroup(9, 3, 4);
        store.addGroup(1, 8, 3);
        store.addGroup(6, 2, 0);

        store.sortByA();

        assertThat(getLastGroup(store), contains(9, 3, 4));

        store.sortByB();

        assertThat(getLastGroup(store), contains(1, 8, 3));

        store.sortByC();

        assertThat(getLastGroup(store), contains(6, 1, 8));
    }

    private List<Integer> getLastGroup(NumberGroupStore store) {
        return List.of(store.getLastA(),
                store.getLastB(),
                store.getLastC());
    }
}
