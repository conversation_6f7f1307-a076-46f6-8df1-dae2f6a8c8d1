package ex23;

import org.junit.jupiter.api.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ArrayMapTests {

    @Test
    @Points(6)
    public void addingNewEntriesIncreasesTheSize() {
        ArrayMap map = new ArrayMap(2);

        assertThat(map.size()).isEqualTo(0);

        map.add("k1", 3);
        assertThat(map.size()).isEqualTo(1);

        map.add("k2", 4);
        assertThat(map.size()).isEqualTo(2);
    }

    @Test
    @Points(6)
    public void canRetrieveStoredValues() {
        ArrayMap map = new ArrayMap(2);

        map.add("k1", 3);
        map.add("k2", 4);

        assertThat(map.get("k1")).isEqualTo(3);
        assertThat(map.get("k2")).isEqualTo(4);
    }

    @Test
    @Points(6)
    public void addingEntryWithExistingKeyOverwritesPreviousValue() {
        ArrayMap map = new ArrayMap(2);

        map.add("k1", 3);
        map.add("k1", 4);

        assertThat(map.size()).isEqualTo(1);
        assertThat(map.get("k1")).isEqualTo(4);
    }

    @Test
    @Points(7)
    public void oldKeysAreOverwrittenIfMapIsFull() {
        ArrayMap map = new ArrayMap(3);

        map.add("k1", 10);
        map.add("k2", 20);
        map.add("k3", 30);
        map.add("k4", 40);
        map.add("k5", 50);

        assertThat(map.size()).isEqualTo(3);

        assertThat(map.values()).containsExactlyInAnyOrder(30, 40, 50);
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseUtilClasses() throws IOException {

        String path = String.format("src/%s.java",
                ArrayMap.class.getName().replaceAll("\\.", "/")) ;

        String source = Files.readString(Paths.get(path));

        assertThat(source).doesNotContain("java.util");
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldHaveOnlyAllowedFields() {
        List<Field> fieldsNotAllowed = Arrays.stream(ArrayMap.class.getDeclaredFields())
                .filter(field -> !field.getType().equals(int.class))
                .filter(field -> !field.getType().equals(Integer.class))
                .filter(field -> !field.getType().equals(Entry[].class))
                .toList();


        assertThat(fieldsNotAllowed).isEmpty();

        List<Field> arrayFields = Arrays.stream(ArrayMap.class.getDeclaredFields())
                .filter(field -> field.getType().equals(Entry[].class))
                .toList();

        assertThat(arrayFields.size()).isEqualTo(1);
    }

}
