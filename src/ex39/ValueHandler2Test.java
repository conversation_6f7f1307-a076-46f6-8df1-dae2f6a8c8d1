package ex39;

import ex39.values.IntegerValue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;
import runner.WarningChecker;

import java.util.List;
import java.util.function.Predicate;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ValueHandler2Test {
    
    @Test
    @Points(4)
    public void canFilterValues() {
        List<IntegerValue> messages = List.of(
            new IntegerValue(3),
            new IntegerValue(6),
            new IntegerValue(1),
            new IntegerValue(8)
        );
        
        Predicate<IntegerValue> criteria = v -> v.intValue() > 3;

        List<IntegerValue> filtered = ValueHandler2.filter(messages, criteria);
        
        assertThat(filtered).hasSize(2);
        assertThat(filtered.getFirst().intValue()).isEqualTo(6);
        assertThat(filtered.getLast().intValue()).isEqualTo(8);
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldCompileWithoutWarnings() {
        assertThat(WarningChecker.hasWarnings(ValueHandler2.class)).isFalse();
        assertThat(WarningChecker.hasWarnings(ValueHandler2Test.class)).isFalse();
    }

}
