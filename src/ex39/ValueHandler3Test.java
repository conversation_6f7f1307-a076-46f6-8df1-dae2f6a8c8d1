package ex39;

import ex39.values.DoubleValue;
import ex39.values.IntegerValue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;
import runner.WarningChecker;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(PointsCounterExtension.class)
public class ValueHandler3Test {
    
    @Test
    @Points(5)
    public void findsLargestNumericValue() {
        var messages = List.of(
            new IntegerValue(5),
            new DoubleValue(3.14),
            new IntegerValue(10),
            new DoubleValue(2.71)
        );
        
        var result = ValueHandler3.findLargest(messages).orElseThrow();

        assertThat(result.intValue()).isEqualTo(10);
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldCompileWithoutWarnings() {
        assertThat(WarningChecker.hasWarnings(ValueHandler3.class)).isFalse();
        assertThat(WarningChecker.hasWarnings(ValueHandler3Test.class)).isFalse();
    }

}
