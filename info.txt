Points
 test1 5+5, 25
 test2 5+5+5, 25

# used on test 0
  ex18 RotatingList
  ex36 Contestant (equals() hashCode())

test 1: algo, array, toString, equals, exceptions, entry
   Odometer (algo),
   ArrayStoreCounter (store number in byte array)
   ConfigLoader (exceptions),
   NumberStore, RotatingList, ArrayDeque, ArrayMap, DimensionStore, CompactMatrix
   WordSearch
   MyLargeNumber (algo)
   DiffCalculator (dates) algo
   Board (gol),
   Board (chunk size)

test 2: generics, enum, inheritance, collections
   PairMap (generics)
   BatchStore (generics)
   ResultsStore (generics)
   NumberGroup (collections, sort, entry)
   GradeRepository (collections, sort, entry)
   DecaNumber (equals, hashCode)
   BatchIterator (collections, algo)
   GradeStore (parse, entry, search)
   StreamAPI(ex35)
   Contestant (equals() hashCode())

exam: polymorphism
   Light on/of
   FormulaComposer
   FormulaGenerator
   Robot (fp, undo); Camera (fp, undo); VersionedDocument (fp, command)
   HexNumber, DecNumber (different operations) (generics)
   ScientificNumber, SimpleNumber

exam: simple
   ScoreCounter (entry) (match member by firstName, name, no)



