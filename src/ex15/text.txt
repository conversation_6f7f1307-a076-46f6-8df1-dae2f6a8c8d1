Ülesanne 2 (36 punkti)

  Paketis ex2 on klass Robot ja test selle testimiseks.

  Klassil Robot on meetodid liikumiseks Põhja, <PERSON><PERSON><PERSON><PERSON>, Itta ja Läände.

  Robot päriselt ei liigu ja lihtsalt kirjutab konsooli, kuhu ta liikus.

  Peaksite lisama koodi, mis peab arvet roboti hetke asukoha kohta.
  Asukohaks on X ja Y koordinaadid matemaatikast tundud kordinaatteljestikus.
  Robot alustab punktist X = 0 ja Y = 0.
  Põhja liikumine tähendab X-i suurendamist ja lõunasse liikumine X-i vähendamist.
  Itta liikumine tähendab Y-i suurendamist ja läände liikumine Y-i vähendamist.

  Lisaks peaksite lisama võimaluse viimase operatsiooni tagasivõtmiseks. Kui robot
  liikus viimasena 2 ühikut läände, siis selle muudatuse tagasivõtmiseks peaks liikuma
  2 ühikut itta. NB! Testid kontrollivad operatsiooni tagasivõtmist vaid koordinaatide
  järgi aga teie peate liikumise meetodeid ka välja kutsuma, mitte lihtsalt koordinaate
  üle kirjutama.

  Kirjutage vajalik kood, et testid tööle hakkaksid.

  Muuta võite klassi Robot. Vajadusel võite uusi klasse juurde luua.
