package ex03;

import org.junit.Test;
import runner.Points;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class OdometerTest {

    private List<String> order = Arrays.asList(
            "AAA", "AAB", "AAC", "ABA", "ABB", "ABC", "ACA", "ACB", "ACC",
            "BAA", "BAB", "BAC", "BBA", "BBB", "BBC", "BCA", "BCB", "BCC",
            "CAA", "CAB", "CAC", "CBA", "CBB", "CBC", "CCA", "CCB", "CCC",
            "AAA"
    );

    @Test
    @Points(1)
    public void firstReadingIsAAA() {
        Odometer odometer = new Odometer();

        assertThat(odometer.getReading(), is("AAA"));
    }

    @Test
    @Points(3)
    public void clickAdvancesWheelOnTheRight() {
        Odometer odometer = new Odometer();

        odometer.click();

        assertThat(odometer.getReading(), is("AAB"));

        odometer.click();

        assertThat(odometer.getReading(), is("AAC"));
    }

    @Test
    @Points(5)
    public void advanceNextWheelWhenFirstHasDoneFullTurn() {
        Odometer odometer = new Odometer();

        odometer.click();
        odometer.click();
        odometer.click();

        assertThat(odometer.getReading(), is("ABA"));
    }

    @Test
    @Points(11)
    public void testAllCombinations() {
        Odometer odometer = new Odometer();

        for (String reading : order) {
            assertThat(odometer.getReading(), is(reading));

            odometer.click();
        }
    }
}
