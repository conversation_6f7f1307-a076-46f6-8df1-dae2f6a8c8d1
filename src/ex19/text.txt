esimene sats
0p: 6
40p: 24
15p: 1
20p: 2

pettev lahendus.
public Integer nextElement() {
        ArrayList<Integer> integers = new ArrayList<>();

        for (Batch batch : batches) {
            integers.addAll(batch.getElements());
        }
paranduseks lisada test
it.nextElement()
it.nextBatch()
it.nextElement()
Selliselt max tulemuste arv 24-lt 12-le.

  Paketis ex1 on klass Batch ja klass BatchIterator ja testid.
  Klass Batch hoiab listi numbritega ja seda klassi tee muuta ei tohi.
  Klass BatchIterator hoiab listi Batch (ports) objektidest ja tagastab neid algse
  järekorra alusel.

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Meetod hasNextBatch() ütleb kas järgmine ports (Batch) on olemas ja meetod nextBatch()
  tagastab järgmise portsu. Seda osa testib meetod iteratorKnowsWhetherThereIsNextBatch().
  <PERSON>in pole veel oluline, et nextBatch() meetod õige portsu tagastab.

  Meetod nextBatch() tagastab järgmise portsu algse järekorra alusel. Kui järgmist portsu
  enam pole, siis viskab meetod IllegalStateException tüüpi erindi. Seda osa kontrollib
  test iteratorReturnsBatchesInOrder().

  Meetod nextElement() tagastab järgmise elemendi portsu seest. Esimesel käivtamisel
  tagastab nextElement() esimese portsu esimese elemendi. Teisel käivitamisel esimese
  portsu järgmise elemendi jne. Kui esimese portsu elemendid on ammendunud võetakse järmine
  ports ja jätkatakse sealt. Selle osa testimiseks on meetod iteratorReturnsElementsFromBatches().

  Meetod nextElement() peaks korrektselt töötama ka siis, kui mõned portsud on tühjad.
  Seda kontrollib test elementIteratorCanHandleEmptyBatches().

  Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
  kirjutada alguses vaid kood, mis esimese testi läbib jne.

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi BatchIterator ja uusi klasse lisada ei või.
    - Klassile BatchIterator võite lisada vaid täisarvtüüpi välju (int või Integer).
