package ex04;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class ScoreCounterTest {

    @Test
    @Points(2)
    public void provideAllData() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(2, "<PERSON><PERSON>", "<PERSON>", 15);

        assertThat(scoreCounter.getPoints(15), is(2));
    }

    @Test
    @Points(2)
    public void provideFirstNameOnly() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(3, "<PERSON><PERSON>", null, null);

        assertThat(scoreCounter.getPoints(6), is(3));
    }

    @Test
    @Points(2)
    public void provideLastNameOnly() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(4, null, "Amigon", null);

        assertThat(scoreCounter.getPoints(11), is(4));
    }

    @Test
    @Points(4)
    public void shouldNotConfuseFirstNameWithLastName() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(5, "Flynn", null, null);
        scoreCounter.addPoints(6, "Martin", null, null);

        assertThat(scoreCounter.getPoints(13), is(5));

        assertThat(scoreCounter.getTeamPoints(), is(11));
    }

    @Test
    @Points(4)
    public void firstAndLastNameBothMustMatch() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(6, "James", "Tollner", null);

        assertThat(scoreCounter.getPoints(1), is(6));

        assertThat(scoreCounter.getTeamPoints(), is(6));
    }

    @Points(3)
    @Test(expected = NoMatchException.class)
    public void shouldThrowIfNoMatches() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(1, "Martin", "Flynn", null);
    }

    @Points(3)
    @Test(expected = MultipleMatchesException.class)
    public void shouldThrowIfMultipleMatches() {
        ScoreCounter scoreCounter = new ScoreCounter();

        scoreCounter.addPoints(1, null, "Martin", null);
    }

    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() {
        Field[] fields = ScoreCounter.class.getDeclaredFields();

        assertThat("ScoreCounter should have only one field",
                fields.length, is(1));
    }


}
