package ex32;

public class ArrayStoreCounter {

    private byte[] store = new byte[3];

    public ArrayStoreCounter(int initialValue) {
        store[0] = (byte) (initialValue % 128);
        initialValue /= 128;
        store[1] = (byte) (initialValue % 128);
        initialValue /= 128;
        store[2] = (byte) (initialValue % 128);
    }

    @Override
    public String toString() {
        return String.valueOf(asInt());
    }

    private int asInt() {
        return store[2] * 128 * 128 + store[1] * 128 + store[0];
    }

    public void increment() {
        store = new ArrayStoreCounter(asInt() + 1).store;
    }
}
