package ex32;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import runner.NoPointsIfThisTestFails;
import runner.Points;
import runner.PointsCounterExtension;

import java.lang.reflect.Field;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;

@ExtendWith(PointsCounterExtension.class)
public class ArrayStoreCounterTest {

    @Test
    @Points(2)
    public void canHoldSmallValues() {
        ArrayStoreCounter number = new ArrayStoreCounter(3);

        assertThat(number.toString()).isEqualTo("3");
        assertThat(new ArrayStoreCounter(7).toString()).isEqualTo("7");
        assertThat(new ArrayStoreCounter(100).toString()).isEqualTo("100");
    }

    @Test
    @Points(2)
    public void canIncrementSmallValues() {
        ArrayStoreCounter number = new ArrayStoreCounter(1);

        number.increment();
        number.increment();

        assertThat(number.toString()).isEqualTo("3");

        for (int i = 3; i < 100; i++) {
            assertThat(number.toString()).isEqualTo(String.valueOf(i));

            number.increment();
        }
    }

    @Test
    @Points(6)
    public void canHoldLargerValues() {
        ArrayStoreCounter number = new ArrayStoreCounter(12345);

        assertThat(number.toString()).isEqualTo("12345");

        assertThat(new ArrayStoreCounter(5555).toString()).isEqualTo("5555");
        assertThat(new ArrayStoreCounter(5432).toString()).isEqualTo("5432");
    }

    @Test
    @Points(10)
    public void canHoldValuesUpTo2Million() {
        for (int i = 0; i < 2_000_000; i++) {
            assertThat(new ArrayStoreCounter(i).toString())
                    .isEqualTo(String.valueOf(i));
        }
    }

    @Test
    @Points(5)
    public void canIncrementValuesUpTo2Million() {
        ArrayStoreCounter number = new ArrayStoreCounter(0);
        for (int i = 0; i < 2_000_000; i++) {
            assertThat(number.toString()).isEqualTo(String.valueOf(i));

            number.increment();
        }
    }

    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() throws IllegalAccessException {
        String message = "Class should have just one field of type byte[]";

        Field[] fields = ArrayStoreCounter.class.getDeclaredFields();

        if (fields.length != 1) {
            fail(message);
        }

        fields[0].setAccessible(true);

        byte[] array = (byte[]) fields[0].get(new ArrayStoreCounter(2_000_000));

        if (array.length > 3) {
            fail("Class should have just one field of type byte[] and size 3");
        }
    }

}