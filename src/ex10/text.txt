Ülesanne 1 (20 punkti)

  NB! See ülesanne on geneeriliste tüüpide (generics) ja polümorfsimi kohta.
      Testid läbiks ka kood, mis nimetatud tehnikaid ei kasuta aga see pole lubatud.
      <PERSON><PERSON><PERSON> lõpus on toodud ka konkreetsemad piirangud.

  Paketis ex1 on klass BatchStore ja test selle testimiseks.

  BatchStore-i saab lisada numbrite komplekte list-i kujul.

     BatchStore store = new BatchStore();
     store.addBatch(List.of(1, 2, 3, 4));

  BatchStore hoiab meeles mitu komplekti on lisatud.
  <PERSON><PERSON> kohta on test batchStoreStoresNumberBatches().

  BatchStore oskab vastata, mis on suurima elementide arvuga komplekti elementide arv.
  <PERSON><PERSON> kohta on test returnsSizeOfLargestBatch().

  BatchStore oskab tagastada komplekti, mille elementide summa on suurim.
  <PERSON><PERSON> kohta on test returnsBatchWithMaxSum().

  NB! Piirangud, mille eiramisel ülesannet ei arvestata.
    Muuta võite vaid klassi BatchStore. Uusi klasse luua ei tohi.
    Andmete hoidmiseks tuleb kasutada liste ja numbreid. Ei sobi andmete
      sõneks teisendamine ja sõne sõelumine (parse) vms.
    Lahenduses ei tohi olla ühtegi cast-i või instanceof operaatorit.
