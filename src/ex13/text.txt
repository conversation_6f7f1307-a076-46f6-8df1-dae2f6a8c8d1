tundub, et selle saab tehtud polümorfismist <PERSON><PERSON>. Liiga palju piiranguid.
esimene sats
0p: 17
7p: 5
15p: 32


Ülesanne 2 (15 punkti)

  NB! See ülesanne on polümorfismi ja meetodite ülelaadimise kohta.
      Soovitud käitumise saaks ka tingimuslausetega teha aga see pole lubatud.
      Piirangud on täpsemalt kirjeldatud ülesande lõpus.

  Klass ex2.FormulaGenerator on klass, mis genereerib valemi objekte.

  Vale<PERSON> objekti liides on määratud failis Formula.java.

  Igal valemi objektil on compute() meetod int ja long tüüpi parameetrite jaoks.

  Klassi ForumlaGenerator meetod getFormulasFor(<konstantide list>) teeb iga konstandi
  kohta ühe valemi objekti ja tagastab need objektid. Valem peaks konstandi väärtuse
  meelde jätma. Tagastatud valemi objektide compute() meetodite käitumie on järgmine:

    - kui compute() meetodi argument on int tüüpi, siis liidetakse argumendile valemiga seotud konstant;
    - kui compute() meetodi argument long tüüpi, siis korrutatakse argument ja valemiga seotud konstant.

  Näide:
    Kui konstant on 1, siis compute(2) tagastab väärtuse 3 (2 + 1).
    Kui konstant on 1, siis compute(2L) tagastab väärtuse 2 (2 * 1).

  Kui enne getFormulasFor() meetodi poole pöördumist käivitada meetod
  reverseFormulas(), siis tagastatakse valemi objektid, mille compute() meetodite
  käitumine on vahetatud (int-i puhul korrutamine, long-i puhul liitmine).

  NB! Piirangud, mille eiramisel ülesannet lahendust ei arvestata.
    - Kogu lahenduse kohta tohib olla vaid üks tsükkel ja üks tingimuslause ning
      need on klassis FormulaGenerator.
    - Muuta võite vaid klasse FormulaGenerator, F1, F2 ja F3 ja uusi klasse lisada ei või.
    - Klasse F1, F2 ja F3 võite kasutada milleks iganes.
    - Klassidel F1, F2 ja F3 võib olla vaid üks täisarvuline väli (int või Integer).
    - Klassil FormulaGenerator võib olla vaid üks boolean tüüpi väli.
