Eksami alguses peaksite käivitama käsu

    Kodus: java -jar monitor.jar <teie bitbuket-i kasutajatunnus>

    Koolis: monitor <teie bitbuket-i kasutajatunnus>

    (olles kataloogis, kuhu te projekti kloonisite).

See programm esitab teie töö automaatselt. Automaatselt laetakse üles vaid need failid,
mille olete salvestanud. Java faile salvestab IDEA automaatselt aga teksti faile mitte.
Kui olete teooriaküsimustele vastanud, siis salvestage see fail. Soovitatav on kontrollida,
kas teie tehtud muudatused on üles laetud. Nupp "Show Uploaded Data avab veebilehe, kust on
näha Git-i logi muudetud failidest ja nende muutmise ajast.

Küsimused laeb monitor alla (faili questions.txt) siis, kui esimene ekraanitõmmis
on üles saadetud.

Aega on 100 minutit. Arvesse läheb seis, mis on esitatud enne kella 17:40.

Teooriaküsimustele saate vastata 20 minuti jooksul alates eksami algusest (16:00).
<PERSON><PERSON><PERSON><PERSON>, mis on tehtud hiljem kui 16:20 ei lähe arvesse.
