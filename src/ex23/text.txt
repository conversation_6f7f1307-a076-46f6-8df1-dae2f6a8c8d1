Ülesanne 1 (25 punkti)

  Paketis ex1 on klass ArrayMap ja test selle testimiseks. <PERSON><PERSON> on
  seal abiklass Entry, mida võite kasutada milleks iganes.

  NB!!! <PERSON><PERSON><PERSON> lõpus on piirangud, mille eiramisel ülesannet ei arvestata.

  ArrayMap on sõnastik, mis võimaldab hoida fikseeritud arvu kirjeid.
  Sõnastiku võti on sõne (String) tüüpi ja väärtus täisarv (int) tüüpi.

  Konstruktori parameeter capacity määrab, kui palju kirjeid saab sõnastikus hoida.
  Kui lisada rohkem kirjeid kirjutatakse varasemad kirjed üle. Esimesena kirjutatakse
  üle kõige varasemalt lisatud kirje.

  Sõnastikul on järgmised meetodid

    add(<key>, <value>) - lisab sõnastikku uue kirje.

    get(<key>) - tagastab väärtuse parameetriga määratud võtme alt.

    size() - <PERSON><PERSON><PERSON>, mitu kirjet sõnastikus on.

    values() - tagastab kõik sõnastikus olevad väärtused täisarvude massiivina.
               Väärtuste järjekord pole oluline.

  Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
  kirjutada alguses vaid kood, mis esimese testi läbib jne.

  NB! Piirangud, mille eiramisel ülesannet ei arvestata.
    - Muuta võite klasse ArrayMap ja Entry. Uusi klasse luua ei tohi.
    - Infot tuleb hoida massiivis ja dünaamilisi kollektsioone kasutada ei või.
    - Klassile ArrayMap võite lisada ainult täisarv tüüpi välju.
    - Kood peab läbima testid klassist ArrayMapTests.
    - Kui @NoPointsIfThisTestFails märgisega test läbi ei lähe, siis lahendust ei arvestata.