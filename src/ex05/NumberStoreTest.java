package ex05;

import org.junit.Test;
import runner.Points;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class NumberStoreTest {

    @Test
    @Points(2)
    public void insertedInOrder() {

        NumberStore store = new NumberStore();

        store.add(1);
        store.add(2);
        store.add(3);

        assertThat(store.getStoredNumbers(), is(new int[] {1, 2, 3}));
    }

    @Test
    @Points(5)
    public void insertedInReverseOrder() {

        NumberStore store = new NumberStore();

        store.add(3);
        store.add(2);
        store.add(1);

        assertThat(store.getStoredNumbers(), is(new int[] {1, 2, 3}));
    }

    @Test
    @Points(5)
    public void zerosAreValidaData() {

        NumberStore store = new NumberStore();

        store.add(0);
        store.add(0);
        store.add(1);
        store.add(2);

        assertThat(store.getStoredNumbers(), is(new int[] {0, 0, 1, 2}));
    }

    @Test
    @Points(5)
    public void handlesInsertionInArbitraryOrder() {
        Random random = new Random(0);

        NumberStore store = new NumberStore();

        for (int i = 0; i < 10 ; i++) {
            store.add(random.nextInt(10));
        }

        assertThat(store.getStoredNumbers(),
                is(new int[] {0, 1, 1, 3, 4, 5, 7, 8, 9, 9}));
    }

    @Test
    public void shouldNotUseClassesFromUtilPackage() {
        String source = Stream.of(NumberStore.class)
                .map(c -> "src/" + c.getName().replaceAll("\\.", "/") + ".java")
                .map(fn -> readSourceFile(Paths.get(fn)))
                .map(contents -> contents.replaceAll("\\s", ""))
                .collect(Collectors.joining());

        assertThat(source, not(containsString("java.util")));
    }

    private String readSourceFile(Path path) {
        try (Stream<String> lines = Files.lines(path)) {
            return lines.collect(Collectors.joining("\n"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
