Ülesanne 1 (17 punkti)

  Paketis ex1 on klass NumberGroupStore ja test selle testimiseks.

  Meetod addGroup() võimaldab lisada kolmest numbrist koosneva numbrite grupi (a, b ja c).

  <PERSON><PERSON> mõni grupp on lisatud, siis saab viimasena lisatud grupi elemendid ükshaaval
  vä<PERSON><PERSON> k<PERSON> (meetodid getLastA(), getLastB(), getLastC()).
  Täpsemaks kirjelduseks on test canGetLastGroupMembers();

  Meetod removeLastGroup() eemaldab viimase grupi.
  Täpsemaks kirjelduseks on test canRemoveLastGroup();

  Meedodid sortByA(), sortByB() ja sortByC() võimaldavad lisatud grupe sorteerida.
  Sorteerimine toimub kasvavas järjekorras meetodis nimes oleva elemendi põhjal (a, b või c).
  Täpsemaks kirjelduseks on test canSortGroupsByGroupMember();

  <PERSON>r<PERSON><PERSON> vajalik kood, et testid tööle hakkaksid.
