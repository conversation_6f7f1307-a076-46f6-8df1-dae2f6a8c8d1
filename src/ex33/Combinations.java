package ex33;

public class Combinations {

    private String value = "FFFF";

    public Combinations(String initialValue) {
    }

    @Override
    public String toString() {
        return null;
    }

    public int asInt() {
        int sum = 0;
        String reversed = new StringBuilder(value).reverse().toString();
        for (int i = 0; i < reversed.length(); i++) {
            char c = reversed.charAt(i);
            int digit = hexDigitToDecimal(c);
            sum += digit * Math.pow(16, i);
        }

        return sum;
    }

    private static int hexDigitToDecimal(char c) {
        return Character.digit(c, 16);
    }


}
