Task 1 (25 points)

NB! At the end of the text are conditions that, if ignored, will invalidate the result.

In the ex30 directory, there is a class (record) MyDate, which represents a date,
and a class DiffCalculator, which allows calculating the number of days between two dates. For example, there are 2 days between May 22 and May 24.

The lengths of months are given in the array found in the field "lengths".
February is marked there as having 28 days, but depending on the year, it can also have 29. The method isLeapYear(<year>) has already been provided and tells you whether February has 29 days that year.

To pass the initial tests, you do not need to consider leap years.

NB! Conditions that, if ignored, will invalidate the result:

You may modify the class DiffCalculator.java. You must not create new classes.

You may not use classes from the java.time or java.util packages.
You also may not copy code from these classes.

The code must pass the tests in the class DiffCalculatorTest.