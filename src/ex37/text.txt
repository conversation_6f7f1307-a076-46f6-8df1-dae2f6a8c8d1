 1p - 1
 9p - 14
16p - 1
18p - 1
25p - 2

mitu üritust testi petta getVersion(): if (index == 0) return ...

  Ülesanne 2 (25 punkti)

  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.
      Testid on järjestatud ülesande keerukuse järjekorras. Seega on soovitatav
      kirjutada alguses vaid kood, mis esimese testi läbib jne.

  Paketis ex2 on klass VersionedDocument ja testid selle käitumise kontrollimiseks.
  VersionedDocument kujutab muudetavat sõne ja operatsioone (append, replace, delete)
  selle muutmiseks.

  Iga muudatus tekitab dokumendist uue versiooni ja hiljem on võimalik iga versiooni vaadata.
  Analoogia Git'iga: iga muudatus on uus commit.

  Lisaks on võimalik fikseerida konkreetse hetke seis (snapshot).
  Analoogia Git'iga: snapshot on kui tag'i lisamine.

  VersionedDocument käitumine on järgmine.

  append(text) - lisab antud teksti dokumenti.

  getContent() - tagastab dokumendi sisu.

  replace(needle, replacement) - asendab dokumendis sõne.

  delete(startIndex, endIndex) - kustutab dokumendist teksti.
                             Vihje: text.substring(0, startIndex) ...

  getVersion(num) - tagastab versiooni num.
                    0 on esimesena tehtud muudatus
                    1 on esimene + teine muudatus
                    jne.

  createSnapshot() - märgib uue vaheseisu.

  getSnapshot(num) - tagastab vaheseisu num.
                     0 on esimene vaheseis
                     1 on teine vaheseis
                     jne.

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite vaid klassi VersionedDocument ja uusi faile lisada ei või.
    - Peate kasutama olemasolevaid välju ja uusi välju lisada ei või.
    - Kood peab läbima testid klassist VersionedDocumentTest.
