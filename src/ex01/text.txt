2. eksam 14/41 0 punkti
         22/41 max

toString()-i kasutamise peale tulemine määrab kogu tulemuse.
Kui seda välja ei mõelnud, siis on 0.

Ülesanne 1 (15 punkti)

    NB! See ülesanne on polümorfismi kohta. Soovitud käitumise saaks ka
        tingimuslausetega teha aga see pole lubatud.
        Piirangud on täpsemalt kirjeldatud ülesande lõpus.

  Paketis ex2 on klass LightController ja test selle testimiseks. LightController koondab
  endas tulesid ja võimaldab neid sisse ja välja lülitada.

  Meetod addLight() lisab uue tule objekti (LightOn või LightOff).

  Kontrolleril on meetod asString(), mis väljastab hetke seisu sõne kujul.
  Iga põleva tule kohta on "O" ja iga kustunud tule kohta "X".
  <PERSON><PERSON><PERSON><PERSON> kirjeldus on testis controllerHasStringRepresentation().

  Meetod turnOn(indeks) lülitab sellel indeksil oleva tule sisse.
  <PERSON><PERSON> kohta on test canTurnLightsOn().

  Meetod toggle(indeks) lülitab sellel indeksil oleva tule ümber (on->off, off->on).
  Selle kohta on test canToggleLights().

  NB! Piirangud, mille eiramisel ülesannet lahendust ei arvestata.
    - Muuta võite vaid klasse LightController, LightOn ja LightOff uusi klasse lisada ei või.
    - Teie koodis ei tohi olla ühtegi tingimuslauset.
    - Info hoidmiseks on kontrolleri väli "lights". Uusi välju kontrollerisse luua ei või.
    - Lubatud on vaid üks tsükkel LightController.asString() meetodis.
