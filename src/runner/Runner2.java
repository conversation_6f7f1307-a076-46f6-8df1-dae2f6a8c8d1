package runner;

import org.junit.runner.Description;
import org.junit.runner.JUnitCore;
import org.junit.runner.notification.Failure;
import org.junit.runner.notification.RunListener;

import java.io.PrintStream;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class Runner2 {

    public static void main(String[] args) {
        String tag = args.length > 0 ? args[0] : "ex29";

        new Runner2().run(tag);
    }

    private void run(String tag) {

        PrintStream out = System.out;

        final PointHolder pointHolder = new PointHolder();

        JUnitCore junit = new JUnitCore();
        junit.addListener(new RunListener() {

            @Override
            public void testStarted(Description description) {
                Points points = description.getAnnotation(Points.class);

                if (points != null) {
                    pointHolder.addToMaxPoints(points.value());
                }
            }

            @Override
            public void testFailure(Failure failure) {
                Points annotation = failure.getDescription()
                        .getAnnotation(Points.class);

                if (annotation != null) {
                    pointHolder.subtract(annotation.value());
                }

                if (failure.getDescription()
                        .getAnnotation(NoPointsIfThisTestFails.class) != null) {

                    pointHolder.subtract(100);
                }
            }

            @Override
            public void testFinished(Description description) {
                Points annotation = description.getAnnotation(Points.class);

                if (annotation != null) {
                    pointHolder.add(annotation.value());
                }
            }
        });

        junit.run(resolveClass(tag));

        String pattern = "{0} of {1} points";

        out.println(MessageFormat.format(pattern,
                Math.max(pointHolder.points, 0), pointHolder.maxPoints));
    }

    private Class<?> resolveClass(String tag) {
        Map<String, String> map = new HashMap<>();
        map.put("ex01", "ex1.FormulaComposerTest");
        map.put("ex02", "ex2.FormulaGeneratorTest");
        map.put("ex03", "ex3.OdometerTest");
        map.put("ex04", "ex4.ScoreCounterTest");
        map.put("ex08", "ex8.PairMapTestSuite");
        map.put("ex17", "ex17.MyLargeNumberTests");
        map.put("ex20", "ex20.ArrayDequeTest");
        map.put("ex22", "ex22.GradeStoreTest");
        map.put("ex29", "ex29.CameraTest");

        if (!map.containsKey(tag)) {
            throw new IllegalStateException("unknown tag: " + tag);
        }

        return loadClass(map.get(tag));
    }

    private Class<?> loadClass(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private static class PointHolder {
        private int points = 0;
        private int maxPoints = 0;

        void add(int points) {
            this.points += points;
        }

        void addToMaxPoints(int points) {
            this.maxPoints += points;
        }

        void subtract(int points) {
            this.points -= points;
        }
    }
}