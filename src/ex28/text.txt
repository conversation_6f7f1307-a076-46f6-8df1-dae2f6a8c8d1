Ülesanne 1 (15 punkti)

  Kataloogis ex1 on klass WordStore ja test selle testimiseks.

  WordStore on klass, mis hoiab sõne (string) kujul numbreid.

  Meetod add() lisab uue kirje.

  Meetod compare() võimaldab võrrelda kahte numbrit. Tulemuseks on täisarv,
  mille väärtus on sama, mis java.lang.Comparable liidese compareTo()
  puhul (-1, 0 või 1).

  Meetod getStoredWords() tagastab sisestatud numbrid sorteerituna.
  Sorteerimine toimub numbrite väärtuse järgi. Nt. "two" on suurem kui "one".

  <PERSON><PERSON><PERSON><PERSON> vajalik kood, et testid tööle hakkaksid.

  Muuta võite klassi WordStore.java. Uusi klasse luua ei tohi.
