package ex22;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.lang.reflect.Field;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class GradeStoreTest {

    @Test
    @Points(5)
    public void providesGradesForFullName() {
        GradeStore gradeStore = new GradeStore();

        assertThat(gradeStore.gradesFor("Samantha", "Adams"),
                equalTo("B, A, B, C"));
    }

    @Test
    @Points(10)
    public void providesGradesForFirstName() {
        GradeStore gradeStore = new GradeStore();

        assertThat(gradeStore.gradesFor("Samantha", null),
                equalTo("B, A, B, C"));
    }

    @Test
    @Points(5)
    public void providesGradesForLastName() {
        GradeStore gradeStore = new GradeStore();

        assertThat(gradeStore.gradesFor(null, "Wright"),
                equalTo("A, C, A, B"));
    }

    @Test(expected = MultipleMatchesException.class)
    @Points(3)
    public void throwsOnMultipleMatches() {
        GradeStore gradeStore = new GradeStore();

        gradeStore.gradesFor(null, "Adams");
    }

    @Test
    @Points(7)
    public void findsStudentWithHighestGPA() {
        GradeStore gradeStore = new GradeStore();

        assertThat(gradeStore.getBestStudent(),
                is("Olivia Lee 4.0"));
    }

    @Test
    @Points(10)
    public void finds5StudentsWithHighestGPA() {
        GradeStore gradeStore = new GradeStore();

        assertThat(gradeStore.getBest5Students(),
                contains("Olivia Lee 4.0",
                        "David Wilson 3.75",
                        "Ashley Garcia 3.6",
                        "Christopher Taylor 3.5",
                        "John Johnson 3.25"));
    }


    @Test
    @NoPointsIfThisTestFails
    public void usesOnlyAllowedFields() {
        Field[] fields = GradeStore.class.getDeclaredFields();

        assertThat(fields.length, is(1));

        assertThat(fields[0].getGenericType().getTypeName(),
                is("java.util.List<ex22.MyClass>"));
    }

}
