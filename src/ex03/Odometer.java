package ex03;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Odometer {

    private LinkedList<Character> alphabet = new LinkedList<>(List.of('A', 'B', 'C'));

    private char wheel1 = alphabet.getFirst();
    private char wheel2 = alphabet.getFirst();
    private char wheel3 = alphabet.getFirst();

    public String getReading() {
        return Stream.of(wheel3, wheel2, wheel1)
                .map(String::valueOf)
                .collect(Collectors.joining());
    }

    public void click() {
        if (wheel1 == alphabet.getLast()) {
            wheel1 = alphabet.getFirst();
            turnWheel2();
        } else {
            wheel1++;
        }
    }

    private void turnWheel2() {
        if (wheel2 == alphabet.getLast()) {
            wheel2 = alphabet.getFirst();
            turnWheel3();
        } else {
            wheel2++;
        }
    }

    private void turnWheel3() {
        if (wheel3 == alphabet.getLast()) {
            wheel3 = alphabet.getFirst();
            wheel2 = alphabet.getFirst();
            wheel1 = alphabet.getFirst();
        } else {
            wheel3++;
        }
    }
}
