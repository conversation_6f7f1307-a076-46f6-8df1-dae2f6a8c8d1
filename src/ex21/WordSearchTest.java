package ex21;

import org.junit.Test;
import runner.NoPointsIfThisTestFails;
import runner.Points;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.hamcrest.CoreMatchers.hasItems;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class WordSearchTest {

    private WordSearch dict = new WordSearch();

    @Test
    @Points(0)
    public void sampleTestForDevelopment() {
        var dict = new WordSearch("a aa aab");

        System.out.println(dict.find("aa"));
    }

    @Test
    @Points(5)
    public void findsWholeMatches() {
        assertThat(dict.find("hello"), contains("hello"));
        assertThat(dict.find("tere"), is(empty()));
    }

    @Test
    @Points(10)
    public void findsMatchesWithSingleWildcardAtTheEnd() {
        assertThat(dict.find("hell*"), contains("hello", "hells"));
    }

    @Test
    @Points(15)
    public void findsMatchesWithDoubleWildcardAtTheEnd() {
        assertThat(dict.find("hel**").size(), is(6));
        assertThat(dict.find("hel**"), hasItems("held", "helical", "helices", "helicon"));
    }

    @Test
    @Points(10)
    public void findsMatchesWithDoubleWildcardAtTheStart() {
        assertThat(dict.find("**hel"),
                contains("breughel", "bushel", "satchel"));
    }

    @Test
    @NoPointsIfThisTestFails
    public void shouldNotUseForbiddenMethods() throws IOException {

        String path = "src/ex21/" + WordSearch.class.getSimpleName() + ".java";

        String source = Files.readString(Paths.get(path));

        assertThat(source, not(containsString("java.util.regex")));
        assertThat(source, not(containsString("endsWith")));
        assertThat(source, not(containsString("startsWith")));
        assertThat(source, not(containsString("contains")));
        assertThat(source, not(containsString("matches")));
    }

}