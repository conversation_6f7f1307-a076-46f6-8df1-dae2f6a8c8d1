ex1:
 0p - 22
 2p - 5
 6p - 16
15p - 4
25p - 5
Total: 52

kehvem sats.


  NB! Ülesande piirangud on teksti lõpus. Nende eiramisel tulemus arvesse ei lähe.

  Paketis ex1 on klassid ContestantName ja Contestant ning
  ContestantTests nende testimiseks.

  ContestantName on lihtne klass, mis hoiab sõne kujul nime.
  Oluline, on siin see, et see käituks sobivalt, kui selline objekt mõnda
  kollektsiooni panna. Oodatud käitumine on kirjeldatud testidega
  canTestWhetherListContainsContestantName() ja doesNotHoldDuplicateNames().

  Contestant on klass, mis kujutab võistlejat. Võistlejal on nimi ja iga
  võistleja kuulub mõnda võistkonda. Võistkonda kuuluvus on kirjeldatud klassis Contestant.
  Klassi Contestant ainuke oluline käitumine on see, et ühes listis ega ühes hulgas ei
  tohi olla kahte sama võistkonda kuuluvat võistlejat.
  Oodatud käitumine on kirjeldatud testidega listContainsOnlyOneContestantFromEachTeam() ja
  setContainsOnlyOneContestantFromEachTeam().

  NB! Piirangud, mille eiramisel lahendust ei arvestata.
    - Muuta võite klasse Contestant ja ContestantName.
      Uusi faile lisada ei või.
    - Teie kirjutatud koodis ei tohi olla võistlejate nimesid (Alice, Bob, ...)