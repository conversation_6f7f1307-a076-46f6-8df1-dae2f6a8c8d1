package ex14;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

public class GradeRepository {

    public List<String> getFullNames() {
        return readData().stream()
                .map(e -> e.getFirstName() + " " + e.getLastName())
                .distinct()
                .collect(Collectors.toList());
    }

    public List<String> getFullNamesSortedByFirstName() {
        return readData().stream()
                .map(e -> e.getFirstName() + " " + e.getLastName())
                .sorted()
                .distinct()
                .collect(Collectors.toList());
    }

    public List<String> getFullNamesSortedByLastName() {
        return readData().stream()
                .sorted(Comparator.comparing(Entry::getLastName))
                .map(e -> e.getFirstName() + " " + e.getLastName())
                .distinct()
                .collect(Collectors.toList());
    }

    public List<String> getFullNamesWithHighestGrade() {
        return readData().stream()
                .collect(toMap(
                        s -> s.getFirstName() + " " + s.getLastName(),
                        s -> s.getGrade(),
                        (a, b) -> Math.max(a, b)))
                .entrySet()
                .stream()
                .map(e -> e.getKey() + " " + e.getValue())
                .collect(Collectors.toList());
    }

    public List<Entry> readData() {
        List<Entry> data = new ArrayList<>();

        for (String line : readLines()) {
            String[] parts = line.split("\\|");
            String firstName = parts[0];
            String lastName = parts[1];
            String subject = parts[2];
            String grade = parts[3];

            data.add(new Entry(firstName, lastName, Integer.parseInt(grade)));

//            System.out.println(firstName);
//            System.out.println(lastName);
//            System.out.println(subject);
//            System.out.println(grade);

        }

        return data;
    }

    private List<String> readLines() {
        try {
            return Files.readAllLines(Paths.get("src/ex14/grades.txt"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



}
